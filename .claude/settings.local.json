{"permissions": {"allow": ["<PERSON><PERSON>(mvn clean:*)", "Bash(./mvnw clean compile)", "Bash(curl -s http://localhost:8080/actuator/health)", "Bash(curl -s http://localhost:8080/api/swagger-test/hello)", "<PERSON><PERSON>(curl:*)", "Bash(./mvnw:*)", "Bash(pkill -f \"cx_ai_slightly\")", "Bash(git add:*)", "Bash(git commit -m \"$(cat <<''EOF''\nfeat: 集成Swagger API文档并修复项目运行问题\n\n* 添加SpringDoc OpenAPI依赖，支持Swagger 3.x\n* 新增SwaggerConfig配置类，自定义API文档信息\n* 创建SwaggerTestController演示Swagger功能\n* 为AuthController和LangChain4jController添加完整的API注解\n* 修复GlobalExceptionHandler中的链式调用编译错误\n* 更新RedisConfig以兼容新版本Spring Boot\n* 配置SaToken白名单允许访问Swagger UI\n* 修复Result类添加链式调用支持\n* 更新validation包名从javax到jakarta\n* 完善SpringDoc配置，支持按标签和操作排序\n\n🤖 Generated with [Claude Code](https://claude.ai/code)\n\nCo-Authored-By: <PERSON> <<EMAIL>>\nEOF\n)\")", "Bash(git push:*)", "Bash(lsof -i :8080)", "Ba<PERSON>(docker logs redis)", "<PERSON><PERSON>(pkill:*)", "Bash(rm:*)", "Bash(lsof:*)", "Bash(kill:*)", "Bash(open http://localhost:8080/api/asr-websocket.html)", "Bash(open http://localhost:8080/api/asr-netty-websocket.html)", "<PERSON><PERSON>(mkdir:*)", "Bash(mvn compile:*)", "Read(//Users/<USER>/Desktop/yuan_dev/ywcx/cx_ai_slightly/src/main/java/com/ywcx/cx_ai_slightly/entity/**)", "Read(//Users/<USER>/Desktop/yuan_dev/ywcx/cx_ai_slightly/src/main/java/com/ywcx/cx_ai_slightly/**)", "<PERSON><PERSON>(docker start:*)", "Bash(find:*)", "WebSearch", "mcp__thinking__sequentialthinking", "Bash(cp:*)", "<PERSON><PERSON>(mvn test)", "Bash(mvn spring-boot:run:*)", "<PERSON><PERSON>(mvn test:*)", "<PERSON><PERSON>(cat:*)", "Bash(java -jar:*)", "Bash(tree:*)"], "deny": []}}