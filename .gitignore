# 项目文档
HELP.md
README.md

# 编译输出目录
target/
out/
build/
bin/
.mvn/wrapper/maven-wrapper.jar
!**/src/main/**/target/
!**/src/test/**/target/
!**/src/main/**/build/
!**/src/test/**/build/

# 日志文件
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# IDE 配置文件
### STS ###
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans
.sts4-cache

### IntelliJ IDEA ###
.idea/
*.iws
*.iml
*.ipr

### NetBeans ###
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/

### VS Code ###
.vscode/

# 操作系统文件
### macOS ###
.DS_Store
.AppleDouble
.LSOverride
Icon
._*

### Windows ###
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msm
*.msp
*.lnk

### Linux ###
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# 依赖目录
node_modules/
jspm_packages/
bower_components/

# 打包文件
*.jar
*.war
*.ear
*.zip
*.tar.gz
*.rar
# 但保留部署目录中的jar文件
!deploy/*.jar

.target/

# 临时文件
*.tmp
*.bak
*.swp
*~.nib
local.properties
.loadpath
.recommenders

# 其他
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.gradle
.codebuddy/sandbox/qr_code.png
