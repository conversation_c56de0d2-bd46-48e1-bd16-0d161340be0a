-- 聊天记录表结构
-- 创建时间: 2025-09-15
-- 数据库: ywcx

USE ywcx;

-- 聊天记录表
CREATE TABLE IF NOT EXISTS `chat_history` (
                                              `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '聊天记录ID，主键',
                                              `user_id` BIGINT NOT NULL COMMENT '用户ID，关联用户表',
                                              `session_id` VARCHAR(64) DEFAULT NULL COMMENT '会话ID，用于标识一次完整的对话',
    `user_message` TEXT NOT NULL COMMENT '用户发送的消息内容',
    `ai_response` TEXT NOT NULL COMMENT 'AI回复的内容',
    `message_type` TINYINT NOT NULL DEFAULT 1 COMMENT '消息类型：1-普通对话，2-天气查询，3-智能问答，4-内容总结，5-文本翻译，6-代码解释，7-创意写作',
    `status` TINYINT NOT NULL DEFAULT 1 COMMENT '记录状态：1-正常，0-已删除',
    `deleted` TINYINT NOT NULL DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_session_id` (`session_id`),
    KEY `idx_create_time` (`create_time`),
    KEY `idx_status` (`status`),
    CONSTRAINT `fk_chat_history_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI聊天记录表';

-- 创建索引优化查询
CREATE INDEX idx_user_session_time ON chat_history(user_id, session_id, create_time);
CREATE INDEX idx_user_type_time ON chat_history(user_id, message_type, create_time);

-- 添加表注释
ALTER TABLE chat_history COMMENT = 'AI聊天记录表，存储用户与AI的对话记录';