-- 用户表结构
-- 创建时间: 2025-09-08
-- 数据库: ywcx

USE ywcx;

-- 用户表
CREATE TABLE IF NOT EXISTS `user` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '用户ID，主键',
    `username` VARCHAR(50) NOT NULL COMMENT '用户名，唯一',
    `password` VARCHAR(255) NOT NULL COMMENT '密码（加密存储）',
    `email` VARCHAR(100) DEFAULT NULL COMMENT '邮箱地址',
    `phone` VARCHAR(20) DEFAULT NULL COMMENT '手机号码',
    `nickname` VARCHAR(50) DEFAULT NULL COMMENT '用户昵称',
    `avatar` VARCHAR(255) DEFAULT NULL COMMENT '头像URL',
    `status` TINYINT NOT NULL DEFAULT 1 COMMENT '用户状态：1-正常，0-禁用，2-未激活',
    `user_type` TINYINT NOT NULL DEFAULT 1 COMMENT '用户类型：1-普通用户，2-管理员',
    `last_login_time` DATETIME DEFAULT NULL COMMENT '最后登录时间',
    `last_login_ip` VARCHAR(50) DEFAULT NULL COMMENT '最后登录IP',
    `login_count` INT NOT NULL DEFAULT 0 COMMENT '登录次数',
    `deleted` TINYINT NOT NULL DEFAULT 0 COMMENT '逻辑删除：0-未删除，1-已删除',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_username` (`username`),
    UNIQUE KEY `uk_email` (`email`),
    KEY `idx_status` (`status`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 用户详情扩展表（可选，用于存储更多用户信息）
CREATE TABLE IF NOT EXISTS `user_profile` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `user_id` BIGINT NOT NULL COMMENT '用户ID',
    `real_name` VARCHAR(50) DEFAULT NULL COMMENT '真实姓名',
    `gender` TINYINT DEFAULT NULL COMMENT '性别：1-男，2-女，0-未知',
    `birthday` DATE DEFAULT NULL COMMENT '生日',
    `address` VARCHAR(255) DEFAULT NULL COMMENT '地址',
    `city` VARCHAR(50) DEFAULT NULL COMMENT '城市',
    `province` VARCHAR(50) DEFAULT NULL COMMENT '省份',
    `country` VARCHAR(50) DEFAULT NULL COMMENT '国家',
    `zip_code` VARCHAR(20) DEFAULT NULL COMMENT '邮编',
    `personal_intro` TEXT COMMENT '个人简介',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user_id` (`user_id`),
    CONSTRAINT `fk_user_profile_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户详情表';

-- 用户登录日志表
CREATE TABLE IF NOT EXISTS `user_login_log` (
    `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `user_id` BIGINT NOT NULL COMMENT '用户ID',
    `login_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '登录时间',
    `login_ip` VARCHAR(50) NOT NULL COMMENT '登录IP',
    `login_user_agent` VARCHAR(500) DEFAULT NULL COMMENT '登录User-Agent',
    `login_result` TINYINT NOT NULL DEFAULT 1 COMMENT '登录结果：1-成功，0-失败',
    `fail_reason` VARCHAR(255) DEFAULT NULL COMMENT '失败原因',
    PRIMARY KEY (`id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_login_time` (`login_time`),
    CONSTRAINT `fk_login_log_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户登录日志表';

-- 插入测试数据
INSERT INTO `user` (`username`, `password`, `email`, `phone`, `nickname`, `status`, `user_type`) VALUES
('admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8i at 4s4hO9xJZqX2tJmJ2sOjJyG', '<EMAIL>', '13800138000', '管理员', 1, 2),
('testuser', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8i at 4s4hO9xJZqX2tJmJ2sOjJyG', '<EMAIL>', '13900139000', '测试用户', 1, 1);

-- 注意：以上密码是示例密文，实际使用时需要使用BCrypt等加密算法加密真实密码
-- 例如，密码"123456"的BCrypt加密结果类似于上面的密文

-- 创建索引优化查询
CREATE INDEX idx_user_username_status ON user(username, status);
CREATE INDEX idx_user_email_status ON user(email, status);
CREATE INDEX idx_user_phone_status ON user(phone, status);
CREATE INDEX idx_user_create_time ON user(create_time);

-- 添加表注释
ALTER TABLE user COMMENT = '用户基础信息表';
ALTER TABLE user_profile COMMENT = '用户详细信息扩展表';
ALTER TABLE user_login_log COMMENT = '用户登录日志记录表';