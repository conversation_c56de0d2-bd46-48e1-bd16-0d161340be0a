# 测试环境配置
spring:
  # 使用内存数据库进行测试
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    username: sa
    password: 
    driver-class-name: org.h2.Driver
    
  # H2数据库控制台（可选）
  h2:
    console:
      enabled: true
      path: /h2-console
  
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: create-drop  # 测试环境每次重新创建表
    show-sql: true
    properties:
      hibernate:
        format_sql: true
        dialect: org.hibernate.dialect.H2Dialect
    open-in-view: false

# 日志配置
logging:
  level:
    root: INFO
    com.ywcx.cx_ai_slightly: DEBUG
    org.hibernate.SQL: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# 禁用一些不需要的自动配置
spring.autoconfigure.exclude:
  - org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration
  - org.springframework.boot.autoconfigure.data.redis.RedisRepositoriesAutoConfiguration
  - cn.dev33.satoken.spring.boot.SaTokenSpringBoot3AutoConfiguration