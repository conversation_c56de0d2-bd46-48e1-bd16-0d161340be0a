package com.ywcx.cx_ai_slightly;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ywcx.cx_ai_slightly.service.AntiCrawlerService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 反爬虫功能测试
 */
@SpringBootTest
@AutoConfigureWebMvc
@ActiveProfiles("test")
public class AntiCrawlerFunctionalTest {

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Autowired
    private AntiCrawlerService antiCrawlerService;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private ObjectMapper objectMapper;

    private MockMvc mockMvc;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
        // 清理Redis缓存
        redisTemplate.getConnectionFactory().getConnection().flushAll();
    }

    @Test
    void testNormalRequest() throws Exception {
        // 测试正常请求
        mockMvc.perform(get("/api/test/normal")
                .header("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.message").value("请求成功"));
    }

    @Test
    void testBlacklistUserAgent() throws Exception {
        // 测试黑名单User-Agent
        mockMvc.perform(get("/api/test/normal")
                .header("User-Agent", "python-requests/2.25.1")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().is(429))
                .andExpect(jsonPath("$.code").value(429))
                .andExpect(jsonPath("$.message").value("请求过于频繁，请稍后再试"));
    }

    @Test
    void testRateLimit() throws Exception {
        // 测试频率限制
        String userAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36";
        
        // 发送100个请求（配置的最大值）
        for (int i = 0; i < 100; i++) {
            mockMvc.perform(get("/api/test/high-frequency")
                    .header("User-Agent", userAgent)
                    .header("X-Forwarded-For", "*************")
                    .contentType(MediaType.APPLICATION_JSON))
                    .andExpect(status().isOk());
        }

        // 第101个请求应该被拒绝
        mockMvc.perform(get("/api/test/high-frequency")
                .header("User-Agent", userAgent)
                .header("X-Forwarded-For", "*************")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().is(429));
    }

    @Test
    void testWhitelistIp() throws Exception {
        // 测试白名单IP（127.0.0.1应该不受限制）
        String userAgent = "python-requests/2.25.1"; // 黑名单User-Agent
        
        mockMvc.perform(get("/api/test/normal")
                .header("User-Agent", userAgent)
                .header("X-Forwarded-For", "127.0.0.1")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk()); // 白名单IP应该通过
    }

    @Test
    void testMissingUserAgent() throws Exception {
        // 测试缺少User-Agent的请求
        mockMvc.perform(get("/api/test/normal")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().is(429));
    }

    @Test
    void testMissingAcceptHeader() throws Exception {
        // 测试缺少Accept头的请求
        mockMvc.perform(get("/api/test/normal")
                .header("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"))
                .andExpect(status().is(429));
    }

    @Test
    void testSuspiciousRequestHeaders() throws Exception {
        // 测试可疑的请求头组合
        mockMvc.perform(get("/api/test/normal")
                .header("User-Agent", "python-requests/2.25.1")
                .header("Connection", "close")
                .header("Accept", "application/json")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().is(429));
    }

    @Test
    void testIpBanAndUnban() throws Exception {
        String testIp = "*************";
        
        // 手动封禁IP
        mockMvc.perform(post("/api/admin/anti-crawler/ip/ban")
                .param("ip", testIp)
                .param("durationSeconds", "60")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data.message").value("IP封禁成功"));

        // 检查IP状态
        mockMvc.perform(get("/api/admin/anti-crawler/ip/status")
                .param("ip", testIp)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data.banned").value(true));

        // 被封禁的IP请求应该被拒绝
        mockMvc.perform(get("/api/test/normal")
                .header("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
                .header("X-Forwarded-For", testIp)
                .header("Accept", "application/json")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().is(429));

        // 解封IP
        mockMvc.perform(post("/api/admin/anti-crawler/ip/unban")
                .param("ip", testIp)
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data.message").value("IP解封成功"));

        // 解封后的IP应该可以正常访问
        mockMvc.perform(get("/api/test/normal")
                .header("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
                .header("X-Forwarded-For", testIp)
                .header("Accept", "application/json")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    void testGetCurrentIp() throws Exception {
        // 测试获取当前IP信息
        mockMvc.perform(get("/api/admin/anti-crawler/ip/current")
                .header("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
                .header("Accept", "application/json")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data.ip").exists())
                .andExpect(jsonPath("$.data.banned").value(false))
                .andExpect(jsonPath("$.data.userAgent").exists());
    }

    @Test
    void testClientIpExtraction() {
        // 测试IP提取逻辑
        // 这里可以通过模拟HttpServletRequest来测试getClientIp方法
        // 由于涉及到Mock对象，这里简化处理
        String testIp = "*************";
        boolean isBanned = antiCrawlerService.isIpBanned(testIp);
        assert !isBanned; // 新IP应该不被封禁
        
        // 封禁IP
        antiCrawlerService.banIp(testIp, 60);
        isBanned = antiCrawlerService.isIpBanned(testIp);
        assert isBanned; // 封禁后应该被检测到
        
        // 解封IP
        antiCrawlerService.unbanIp(testIp);
        isBanned = antiCrawlerService.isIpBanned(testIp);
        assert !isBanned; // 解封后应该不被封禁
    }
}