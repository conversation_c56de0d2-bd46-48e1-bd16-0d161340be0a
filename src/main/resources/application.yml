spring:
  application:
    name: cx_ai_slightly
  profiles:
    active: dev
  
  # Spring Data JPA 配置
  jpa:
    hibernate:
      ddl-auto: update  # 生产环境建议使用validate或none
    show-sql: true
    properties:
      hibernate:
        format_sql: true
        use_sql_comments: true
        dialect: org.hibernate.dialect.MySQLDialect
        
        # 逻辑删除配置
        default_batch_fetch_size: 16
        jdbc:
          batch_size: 20
    open-in-view: false  # 禁用OpenEntityManagerInViewFilter，避免懒加载问题

# LangChain4j 配置
langchain4j:
  dashscope:
    chat-model:
      api-key: ${DASHSCOPE_API_KEY:sk-cfcf4c361f5f4c8fb6d15b5621782024}
      model-name: qwen-turbo
      temperature: 0.7
    embedding-model:
      api-key: ${DASHSCOPE_API_KEY:sk-cfcf4c361f5f4c8fb6d15b5621782024}
      model-name: text-embedding-v1

# Sa-Token 配置
sa-token:
  # token 名称 (同时也是 cookie 名称)
  token-name: satoken
  # token 有效期，单位s 默认30天, -1代表永不过期
  timeout: 2592000
  # token 临时有效期 (指定时间内无操作就视为token过期) 单位: 秒
  active-timeout: -1
  # 是否允许同一账号并发登录 (为true时允许一起登录, 为false时新登录挤掉旧登录)
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个token (为true时所有登录共用一个token, 为false时每次登录新建一个token)
  is-share: true
  # token风格
  token-style: uuid
  # 是否输出操作日志
  is-log: false
  # 是否从cookie中读取token
  is-read-cookie: false
  # 是否从head中读取token
  is-read-head: true
  # 是否从body中读取token
  is-read-body: false

# SpringDoc OpenAPI 配置
springdoc:
  api-docs:
    enabled: true
    path: /v3/api-docs
  swagger-ui:
    enabled: true
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
    display-request-duration: true
    show-extensions: true
    show-common-extensions: true
  group-configs:
    - group: 'default'
      paths-to-match: '/api/**'
      packages-to-scan: com.ywcx.cx_ai_slightly.controller

# 反爬虫配置
anti-crawler:
  enabled: true
  rate-limit:
    window-seconds: 60        # 时间窗口（秒）
    max-requests: 100         # 最大请求次数
    ban-duration-seconds: 300 # 封禁时间（秒）
  blacklist-user-agents:
    - "python-requests"
    - "curl"
    - "wget"
    - "scrapy"
    - "bot"
    - "spider"
    - "crawler"
    - "postman"
  blacklist-ips: []
  whitelist-ips:
    - "127.0.0.1"
    - "localhost"
    - "::1"
  captcha-required-paths:
    - "/api/auth/login"
    - "/api/auth/register"
    - "/api/user/sensitive"

# Netty WebSocket 配置
netty:
  port: 8081