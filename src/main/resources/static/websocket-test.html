<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Netty WebSocket ASR Test</title>
    <style>
        body { font-family: sans-serif; }
        #container { max-width: 800px; margin: 20px auto; padding: 20px; border: 1px solid #ccc; border-radius: 5px; }
        .control-group { margin-bottom: 15px; padding-bottom: 15px; border-bottom: 1px solid #eee; }
        #messages { height: 300px; border: 1px solid #ddd; overflow-y: scroll; padding: 10px; background-color: #f9f9f9; }
        .message { margin-bottom: 5px; padding: 5px; border-radius: 3px; }
        .sent { background-color: #e1ffc7; text-align: right; }
        .received { background-color: #d4edda; color: #155724; }
        .system { background-color: #fffbe6; font-style: italic; text-align: center; }
        .error { background-color: #f8d7da; color: #721c24; }
        .audio-url { background-color: #cce5ff; color: #004085; }
        #status { display: inline-block; padding: 5px 10px; border-radius: 15px; color: white; }
        .connected { background-color: #28a745; }
        .disconnected { background-color: #dc3545; }
        input[type="text"] { width: 300px; padding: 8px; }
        button { padding: 8px 15px; cursor: pointer; margin-right: 5px; }
        
        /* 音频播放器样式 */
        #audioPlayerContainer {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f8f9fa;
            display: none;
        }
        #audioPlayer {
            width: 100%;
            margin-top: 10px;
        }
        .player-controls {
            margin-top: 10px;
        }
        .player-controls button {
            margin-right: 10px;
        }
    </style>
</head>
<body>

<div id="container">
    <h2>Real-time Speech Recognition Test</h2>
    <div id="connection-controls" class="control-group">
        <button id="connectBtn">Connect</button>
        <button id="disconnectBtn" disabled>Disconnect</button>
        <span>Status: <span id="status" class="disconnected">Disconnected</span></span>
    </div>

    <div id="audio-controls" class="control-group">
        <h4>Audio Recording (16kHz, 16-bit PCM)</h4>
        <button id="startRecordBtn" disabled>Start Recording</button>
        <button id="stopRecordBtn" disabled>Stop Recording</button>
    </div>

    <div id="messages"></div>
    
    <!-- Audio Player Section -->
    <div id="audioPlayerContainer">
        <h4>Generated Audio Playback</h4>
        <div id="player-status">Player is idle.</div>
        <!-- The actual audio elements are hidden -->
    </div>
</div>

<script>
    // ... (existing WebSocket and recording code) ...
    const connectBtn = document.getElementById('connectBtn');
    const disconnectBtn = document.getElementById('disconnectBtn');
    const messagesDiv = document.getElementById('messages');
    const statusSpan = document.getElementById('status');
    const startRecordBtn = document.getElementById('startRecordBtn');
    const stopRecordBtn = document.getElementById('stopRecordBtn');

    let websocket = null;
    const wsUri = "ws://localhost:8081/ws";

    // Web Audio API variables
    let audioContext;
    let scriptProcessor;
    let mediaStreamSource;
    const TARGET_SAMPLE_RATE = 16000;

    function updateStatus(connected) {
        if (connected) {
            statusSpan.textContent = 'Connected';
            statusSpan.className = 'connected';
            connectBtn.disabled = true;
            disconnectBtn.disabled = false;
            startRecordBtn.disabled = false;
        } else {
            statusSpan.textContent = 'Disconnected';
            statusSpan.className = 'disconnected';
            connectBtn.disabled = false;
            disconnectBtn.disabled = true;
            if (scriptProcessor) {
                stopRecordingFlow();
            }
            startRecordBtn.disabled = true;
            stopRecordBtn.disabled = true;
            websocket = null;
        }
    }

    function logMessage(type, message) {
        const msgDiv = document.createElement('div');
        msgDiv.className = 'message ' + type;
        msgDiv.textContent = message;
        messagesDiv.appendChild(msgDiv);
        messagesDiv.scrollTop = messagesDiv.scrollHeight;
    }

    connectBtn.onclick = function() {
        if (websocket) return;
        logMessage('system', 'Connecting to ' + wsUri + '...');
        websocket = new WebSocket(wsUri);
        websocket.binaryType = 'arraybuffer';

        websocket.onopen = function(evt) {
            logMessage('system', 'Connection established.');
            updateStatus(true);
        };

        websocket.onmessage = function(evt) {
            const data = evt.data;
            if (typeof data === 'string') {
                if (data.startsWith("TTS_URL:")) {
                    const audioUrl = data.substring(8);
                    logMessage('audio-url', `Received Audio URL: ${audioUrl}`);
                    onReceiveAudioUrl(audioUrl); // Use the new gapless player logic
                } else {
                    logMessage('received', `Server: ${data}`);
                }
            } else {
                // Handle binary data if necessary
                logMessage('system', 'Received binary data of length: ' + data.byteLength);
            }
        };

        websocket.onclose = function(evt) {
            logMessage('system', 'Connection closed.');
            updateStatus(false);
        };

        websocket.onerror = function(evt) {
            logMessage('error', 'WebSocket error. See console for details.');
            console.error("WebSocket error:", evt);
        };
    };

    disconnectBtn.onclick = function() {
        if (websocket) {
            websocket.close();
        }
    };

    // --- Web Audio API Logic (unchanged) ---
    async function startRecordingFlow() {
        if (!websocket || websocket.readyState !== WebSocket.OPEN) {
            logMessage('error', 'WebSocket is not connected.');
            return;
        }
        websocket.send("START");
        logMessage('sent', 'SENT: START command');
        try {
            const stream = await navigator.mediaDevices.getUserMedia({ audio: true, video: false });
            audioContext = new (window.AudioContext || window.webkitAudioContext)({ sampleRate: TARGET_SAMPLE_RATE });
            if (audioContext.sampleRate !== TARGET_SAMPLE_RATE) {
                logMessage('system', `AudioContext sample rate is ${audioContext.sampleRate}. Resampling to ${TARGET_SAMPLE_RATE}Hz.`);
            } else {
                logMessage('system', `AudioContext sample rate is ${audioContext.sampleRate}Hz.`);
            }
            mediaStreamSource = audioContext.createMediaStreamSource(stream);
            const bufferSize = 4096;
            scriptProcessor = audioContext.createScriptProcessor(bufferSize, 1, 1);
            scriptProcessor.onaudioprocess = function(audioProcessingEvent) {
                if (!websocket || websocket.readyState !== WebSocket.OPEN) return;
                const inputData = audioProcessingEvent.inputBuffer.getChannelData(0);
                const resampledData = resample(inputData, audioContext.sampleRate, TARGET_SAMPLE_RATE);
                const pcm16Data = floatTo16BitPCM(resampledData);
                websocket.send(pcm16Data.buffer);
            };
            mediaStreamSource.connect(scriptProcessor);
            scriptProcessor.connect(audioContext.destination);
            logMessage('system', 'Recording started...');
            startRecordBtn.disabled = true;
            stopRecordBtn.disabled = false;
        } catch (err) {
            logMessage('error', 'Error accessing microphone: ' + err.message);
            if (websocket && websocket.readyState === WebSocket.OPEN) websocket.send("STOP");
        }
    }

    function stopRecordingFlow() {
        if (mediaStreamSource) {
            mediaStreamSource.mediaStream.getTracks().forEach(track => track.stop());
            mediaStreamSource.disconnect();
        }
        if (scriptProcessor) {
            scriptProcessor.disconnect();
            scriptProcessor = null;
        }
        if (audioContext) {
            audioContext.close();
            audioContext = null;
        }
        logMessage('system', 'Recording stopped locally.');
        startRecordBtn.disabled = false;
        stopRecordBtn.disabled = true;
        if (websocket && websocket.readyState === WebSocket.OPEN) {
            websocket.send("STOP");
            logMessage('sent', 'SENT: STOP command');
        }
    }

    startRecordBtn.onclick = startRecordingFlow;
    stopRecordBtn.onclick = stopRecordingFlow;

    function resample(inputBuffer, inputSampleRate, outputSampleRate) {
        if (inputSampleRate === outputSampleRate) return inputBuffer;
        const sampleRateRatio = inputSampleRate / outputSampleRate;
        const newLength = Math.round(inputBuffer.length / sampleRateRatio);
        const result = new Float32Array(newLength);
        let offsetResult = 0, offsetBuffer = 0;
        while (offsetResult < result.length) {
            const nextOffsetBuffer = Math.round((offsetResult + 1) * sampleRateRatio);
            let accum = 0, count = 0;
            for (let i = offsetBuffer; i < nextOffsetBuffer && i < inputBuffer.length; i++) {
                accum += inputBuffer[i];
                count++;
            }
            result[offsetResult] = count > 0 ? accum / count : 0;
            offsetResult++;
            offsetBuffer = nextOffsetBuffer;
        }
        return result;
    }

    function floatTo16BitPCM(input) {
        const output = new Int16Array(input.length);
        for (let i = 0; i < input.length; i++) {
            const s = Math.max(-1, Math.min(1, input[i]));
            output[i] = s < 0 ? s * 0x8000 : s * 0x7FFF;
        }
        return output;
    }

    // --- Gapless Audio Playback Logic ---
    const playerStatusDiv = document.getElementById('player-status');
    const audioPlayers = [new Audio(), new Audio()];
    const audioQueue = [];
    let currentPlayerIndex = 0;
    let isPlaying = false;

    function updatePlayerStatus(message) {
        console.log("Player Status:", message);
        playerStatusDiv.textContent = message;
    }

    function playNextInQueue() {
        if (audioQueue.length === 0) {
            isPlaying = false;
            updatePlayerStatus('Queue finished. Player is idle.');
            return;
        }

        isPlaying = true;
        const nextUrl = audioQueue.shift();
        const activePlayer = audioPlayers[currentPlayerIndex];
        
        updatePlayerStatus(`Playing: ${nextUrl.substring(nextUrl.lastIndexOf('/') + 1)}`);
        activePlayer.src = nextUrl;
        activePlayer.play().catch(e => {
            console.error("Playback error:", e);
            updatePlayerStatus(`Error playing audio: ${e.message}`);
            playNextInQueue(); // Try the next one
        });

        // Preload the next track if available
        if (audioQueue.length > 0) {
            const nextPlayerIndex = (currentPlayerIndex + 1) % 2;
            const nextPlayer = audioPlayers[nextPlayerIndex];
            updatePlayerStatus(`Playing... (Pre-loading next track)`);
            nextPlayer.src = audioQueue[0];
            nextPlayer.load();
        }
    }

    // Setup event listeners for both audio players
    audioPlayers.forEach((player, index) => {
        player.addEventListener('ended', () => {
            logMessage('system', `Finished track on player ${index}.`);
            currentPlayerIndex = (currentPlayerIndex + 1) % 2;
            playNextInQueue();
        });

        player.addEventListener('error', (e) => {
            logMessage('error', `Error on player ${index}: ${e.target.error.message}`);
            playNextInQueue(); // Try to recover by playing the next in queue
        });
    });

    // This function is called by the WebSocket 'onmessage' handler
    function onReceiveAudioUrl(url) {
        document.getElementById('audioPlayerContainer').style.display = 'block';
        audioQueue.push(url);
        updatePlayerStatus(`URL added to queue. Total: ${audioQueue.length}`);
        if (!isPlaying) {
            playNextInQueue();
        }
    }

</script>

</body>
</html>
