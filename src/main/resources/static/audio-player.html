<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gapless Audio Player</title>
    <style>
        body { 
            font-family: sans-serif; 
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background-color: #f0f2f5;
        }
        .player-container {
            border: 1px solid #ccc;
            border-radius: 8px;
            padding: 20px;
            background-color: #ffffff;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h2 {
            text-align: center;
            color: #333;
        }
        textarea {
            width: 100%;
            padding: 10px;
            box-sizing: border-box;
            margin-top: 5px;
            border-radius: 4px;
            border: 1px solid #ccc;
            height: 150px;
        }
        button {
            padding: 10px 15px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            transition: background-color 0.2s;
        }
        button:hover {
            background-color: #0056b3;
        }
        .control-group {
            margin-bottom: 15px;
            text-align: center;
        }
        .status {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            background-color: #e9ecef;
            text-align: center;
        }
        #player-wrapper {
            display: none; /* Hide the actual players */
        }
    </style>
</head>
<body>
    <div class="player-container">
        <h2>Gapless Audio Queue Player</h2>
        
        <div class="control-group">
            <label for="ttsUrls">Simulate TTS URLs (one per line):</label>
            <textarea id="ttsUrls" placeholder="Enter one audio URL per line. They will be added to the queue automatically.">https://ywcx-ai-bot.oss-cn-beijing.aliyuncs.com/audio-service_temp2d016b39a34c4d75aa9be9905c1e7cf9.mp3
https://ywcx-ai-bot.oss-cn-beijing.aliyuncs.com/audio-service_temp8b9e3b5c1a0d4e8a9a5b1e2e3f4d5a6b.mp3
https://ywcx-ai-bot.oss-cn-beijing.aliyuncs.com/audio-service_temp9c0a1b2c3d4e5f6a7b8c9d0e1f2a3b4c.mp3</textarea>
            <button id="addBtn">Add URLs to Queue</button>
        </div>
        
        <div id="status" class="status">Player is idle. Add URLs to start.</div>

        <div id="player-wrapper">
            <audio id="audioPlayer0"></audio>
            <audio id="audioPlayer1"></audio>
        </div>
    </div>

    <script>
        const addBtn = document.getElementById('addBtn');
        const ttsUrlsText = document.getElementById('ttsUrls');
        const statusDiv = document.getElementById('status');

        const audioPlayers = [new Audio(), new Audio()];
        const audioQueue = [];
        let currentPlayerIndex = 0;
        let isPlaying = false;

        // Function to update status display
        function updateStatus(message) {
            console.log(message);
            statusDiv.textContent = message;
        }

        // Core function to play the next audio in the queue
        function playNextInQueue() {
            if (audioQueue.length === 0) {
                isPlaying = false;
                updateStatus('Queue finished. Player is idle.');
                return;
            }

            isPlaying = true;
            const nextUrl = audioQueue.shift();
            const activePlayer = audioPlayers[currentPlayerIndex];
            
            updateStatus(`Playing: ${nextUrl}`);
            activePlayer.src = nextUrl;
            activePlayer.play().catch(e => {
                console.error("Playback error:", e);
                updateStatus(`Error playing audio: ${e.message}`)
                // If one track fails, try the next one
                playNextInQueue();
            });

            // Preload the next track if available
            if (audioQueue.length > 0) {
                const nextPlayerIndex = (currentPlayerIndex + 1) % 2;
                const nextPlayer = audioPlayers[nextPlayerIndex];
                updateStatus(`Pre-loading next: ${audioQueue[0]}`);
                nextPlayer.src = audioQueue[0];
                nextPlayer.load();
            }
        }

        // Set up event listeners for both audio players
        audioPlayers.forEach((player, index) => {
            player.addEventListener('ended', () => {
                updateStatus(`Finished track. Moving to next.`);
                currentPlayerIndex = (currentPlayerIndex + 1) % 2;
                playNextInQueue();
            });

            player.addEventListener('error', (e) => {
                updateStatus(`Error on player ${index}: ${e.target.error.message}`);
                // Try to recover by playing the next in queue
                playNextInQueue();
            });
        });

        // Function to add URLs from the textarea to the queue
        function addUrlsToQueue() {
            const urls = ttsUrlsText.value.trim().split('\n').filter(url => url.trim() !== '');
            if (urls.length === 0) return;

            urls.forEach(url => audioQueue.push(url.trim()));
            ttsUrlsText.value = ''; // Clear the textarea
            updateStatus(`${urls.length} URL(s) added to the queue.`);

            if (!isPlaying) {
                playNextInQueue();
            }
        }

        // Event listener for the button
        addBtn.addEventListener('click', addUrlsToQueue);

        // This would be called by your WebSocket 'onmessage' handler
        // For demonstration, we use the button and textarea.
        function onWebSocketMessage(url) {
            audioQueue.push(url);
            if (!isPlaying) {
                playNextInQueue();
            }
        }

    </script>
</body>
</html>