# 测试环境配置
spring:
  # 数据源配置 - 使用内存数据库H2
  datasource:
    url: jdbc:h2:mem:testdb
    username: sa
    password: 
    driver-class-name: org.h2.Driver
    hikari:
      maximum-pool-size: 5
      minimum-idle: 2

  # Redis 配置 - 使用本地Redis
  data:
    redis:
      host: localhost
      port: 6379
      password: 
      database: 1  # 使用不同的数据库避免冲突
      timeout: 5000ms
      lettuce:
        pool:
          max-active: 4
          max-idle: 4
          min-idle: 0
          max-wait: -1ms

  # H2数据库控制台配置（测试用）
  h2:
    console:
      enabled: true

# 服务器配置 - 不设置context-path
server:
  port: 0  # 使用随机端口，避免端口冲突

# 日志配置 - 简化测试日志
logging:
  level:
    root: WARN
    com.ywcx.cx_ai_slightly: INFO
    com.ywcx.cx_ai_slightly.interceptor.AntiCrawlerInterceptor: DEBUG
  pattern:
    console: "%d{HH:mm:ss} %-5level %logger{36} - %msg%n"

# 关闭不必要的组件
management:
  endpoints:
    enabled-by-default: false
    web:
      exposure:
        include: health

# 反爬虫测试配置
anti-crawler:
  enabled: true
  rate-limit:
    window-seconds: 60
    max-requests: 5  # 降低阈值便于测试
    ban-duration-seconds: 60
  blacklist-user-agents:
    - "python-requests"
    - "curl"
    - "scrapy"
  whitelist-ips:
    - "127.0.0.1"
    - "localhost"
    - "::1"