# 开发环境配置
spring:
  # 数据源配置
  datasource:
    url: **********************************************************************************************************************************************
    username: root
    password: 123456
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 10
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000

  # Redis 配置
  data:
    redis:
      host: localhost
      port: 6379
      password: 123456
      database: 0
      timeout: 5000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0
          max-wait: -1ms

# 服务器配置
server:
  port: 8080
  servlet:
    context-path: /

# 开发环境特定配置
debug: true

# JPA日志配置
logging:
  level:
    root: INFO
    com.ywcx.cx_ai_slightly: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
    org.hibernate.type.descriptor.sql.BasicExtractor: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/cx_ai_slightly-dev.log

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always

# 阿里语音识别配置
aliyun:
  nls:
    app-key: wzTMlc11UTJuBs0t
    access-key-id: LTAI5t5sQrWb5xKrCYUzXT6Q
    access-key-secret: ******************************
    gateway-url: ${NLS_GATEWAY_URL:wss://nls-gateway-cn-beijing.aliyuncs.com/ws/v1}


