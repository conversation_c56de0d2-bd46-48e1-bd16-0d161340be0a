package com.ywcx.cx_ai_slightly.entity;

import jakarta.persistence.*;
import org.hibernate.annotations.SQLRestriction;
import com.ywcx.cx_ai_slightly.entity.listener.UserEntityListener;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

/**
 * 用户实体类
 * 
 * 对应数据库中的user表，存储用户基础信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "user")
@SQLRestriction("is_delete = 0")
@EntityListeners(UserEntityListener.class)
public class User {

    /**
     * 用户ID，主键
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 用户名，唯一
     */
    private String username;

    /**
     * 密码（加密存储）
     */
    private String password;


    /**
     * 手机号码
     */
    private String phone;

    /**
     * 用户昵称
     */
    private String nickname;

    /**
     * 头像URL
     */
    private String avatar;

    /**
     * 用户状态：1-正常，0-禁用，2-未激活
     */
    private Integer status;

    /**
     * 用户类型：1-普通用户，2-管理员
     */
    @Column(name = "user_type")
    private Integer userType;

    /**
     * 最后登录时间
     */
    @Column(name = "last_login_time")
    private LocalDateTime lastLoginTime;

    /**
     * 最后登录IP
     */
    @Column(name = "last_login_ip")
    private String lastLoginIp;

    /**
     * 登录次数
     */
    @Column(name = "login_count")
    private Integer loginCount;

    /**
     * 逻辑删除：0-未删除，1-已删除
     */
    @Column(name = "is_delete")
    private Integer deleted;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private LocalDateTime updateTime;

    /**
     * 获取用户状态枚举
     */
    public UserStatus getUserStatus() {
        return UserStatus.fromCode(this.status);
    }

    /**
     * 获取用户类型枚举
     */
    public UserType getUserTypeEnum() {
        return UserType.fromCode(this.userType);
    }

    /**
     * 用户状态枚举
     */
    public enum UserStatus {
        DISABLED(0, "禁用"),
        ACTIVE(1, "正常"),
        INACTIVE(2, "未激活");

        private final int code;
        private final String description;

        UserStatus(int code, String description) {
            this.code = code;
            this.description = description;
        }

        public static UserStatus fromCode(Integer code) {
            if (code == null) return null;
            for (UserStatus status : values()) {
                if (status.code == code) {
                    return status;
                }
            }
            return null;
        }

        public int getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 用户类型枚举
     */
    public enum UserType {
        NORMAL(1, "普通用户"),
        ADMIN(2, "管理员");

        private final int code;
        private final String description;

        UserType(int code, String description) {
            this.code = code;
            this.description = description;
        }

        public static UserType fromCode(Integer code) {
            if (code == null) return null;
            for (UserType type : values()) {
                if (type.code == code) {
                    return type;
                }
            }
            return null;
        }

        public int getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }
}