package com.ywcx.cx_ai_slightly.entity.listener;

import com.ywcx.cx_ai_slightly.entity.User;
import jakarta.persistence.PrePersist;
import jakarta.persistence.PreUpdate;

import java.time.LocalDateTime;

/**
 * 用户实体监听器
 * 
 * 用于自动设置实体的创建时间和更新时间
 */
public class UserEntityListener {

    /**
     * 在实体持久化之前调用
     * 设置创建时间和更新时间
     */
    @PrePersist
    public void prePersist(User user) {
        LocalDateTime now = LocalDateTime.now();
        if (user.getCreateTime() == null) {
            user.setCreateTime(now);
        }
        if (user.getUpdateTime() == null) {
            user.setUpdateTime(now);
        }
        if (user.getDeleted() == null) {
            user.setDeleted(0);
        }
    }

    /**
     * 在实体更新之前调用
     * 设置更新时间
     */
    @PreUpdate
    public void preUpdate(User user) {
        user.setUpdateTime(LocalDateTime.now());
    }
}