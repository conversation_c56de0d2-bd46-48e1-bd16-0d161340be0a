package com.ywcx.cx_ai_slightly.entity.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 系统信息响应实体
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "系统信息响应")
public class SystemInfoResponse {
    
    @Schema(description = "当前时间", example = "2024-01-01T12:00:00")
    private LocalDateTime time;
    
    @Schema(description = "版本号", example = "1.0.0")
    private String version;
    
    @Schema(description = "系统描述", example = "CX AI Slightly 系统")
    private String description;
}