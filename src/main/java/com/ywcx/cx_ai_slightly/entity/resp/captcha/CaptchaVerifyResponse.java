package com.ywcx.cx_ai_slightly.entity.resp.captcha;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 验证码验证响应实体
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "验证码验证响应")
public class CaptchaVerifyResponse {
    
    @Schema(description = "验证是否有效", example = "true")
    private Boolean valid;
    
    @Schema(description = "验证消息", example = "验证成功")
    private String message;
}