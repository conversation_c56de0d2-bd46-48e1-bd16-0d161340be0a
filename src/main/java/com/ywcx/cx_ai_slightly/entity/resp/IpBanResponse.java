package com.ywcx.cx_ai_slightly.entity.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * IP封禁响应实体
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "IP封禁响应")
public class IpBanResponse {
    
    @Schema(description = "操作消息", example = "IP封禁成功")
    private String message;
    
    @Schema(description = "IP地址", example = "***********")
    private String ip;
    
    @Schema(description = "封禁时长（秒）", example = "3600")
    private Integer duration;
}