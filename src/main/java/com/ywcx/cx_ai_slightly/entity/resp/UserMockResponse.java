package com.ywcx.cx_ai_slightly.entity.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 用户模拟信息响应实体
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "用户模拟信息响应")
public class UserMockResponse {
    
    @Schema(description = "用户ID", example = "123")
    private Long id;
    
    @Schema(description = "用户名", example = "测试用户123")
    private String name;
    
    @Schema(description = "邮箱", example = "<EMAIL>")
    private String email;
    
    @Schema(description = "创建时间", example = "2024-01-01T12:00:00")
    private LocalDateTime createTime;
}