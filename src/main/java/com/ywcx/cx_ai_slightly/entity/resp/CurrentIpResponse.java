package com.ywcx.cx_ai_slightly.entity.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 当前IP信息响应实体
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "当前IP信息响应")
public class CurrentIpResponse {
    
    @Schema(description = "IP地址", example = "***********")
    private String ip;
    
    @Schema(description = "是否被封禁", example = "false")
    private Boolean banned;
    
    @Schema(description = "用户代理", example = "Mozilla/5.0 (Windows NT 10.0; Win64; x64)...")
    private String userAgent;
}