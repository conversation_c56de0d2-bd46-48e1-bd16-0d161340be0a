package com.ywcx.cx_ai_slightly.entity.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 反爬虫测试响应实体
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "反爬虫测试响应")
public class
AntiCrawlerTestResponse {
    
    @Schema(description = "测试消息", example = "请求成功")
    private String message;
    
    @Schema(description = "客户端IP", example = "***********")
    private String clientIp;
    
    @Schema(description = "用户代理", example = "Mozilla/5.0 (Windows NT 10.0; Win64; x64)...")
    private String userAgent;
}