package com.ywcx.cx_ai_slightly.entity.resp.anticrawler;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * IP状态响应实体
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "IP状态响应")
public class IpStatusResponse {
    
    @Schema(description = "IP地址", example = "***********")
    private String ip;
    
    @Schema(description = "是否被封禁", example = "false")
    private Boolean banned;
}