package com.ywcx.cx_ai_slightly.entity.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * IP解封响应实体
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "IP解封响应")
public class IpUnbanResponse {
    
    @Schema(description = "操作消息", example = "IP解封成功")
    private String message;
    
    @Schema(description = "IP地址", example = "***********")
    private String ip;
}