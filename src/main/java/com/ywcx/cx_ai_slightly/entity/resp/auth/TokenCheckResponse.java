package com.ywcx.cx_ai_slightly.entity.resp.auth;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Token检查响应实体
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Token检查响应")
public class TokenCheckResponse {
    
    @Schema(description = "是否已登录", example = "true")
    private Boolean isLogin;
    
    @Schema(description = "登录ID", example = "user123")
    private String loginId;
    
    @Schema(description = "Token值", example = "xxx")
    private String tokenValue;
    
    @Schema(description = "Token超时时间", example = "2592000")
    private Long tokenTimeout;
}