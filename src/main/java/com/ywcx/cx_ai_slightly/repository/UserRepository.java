package com.ywcx.cx_ai_slightly.repository;

import com.ywcx.cx_ai_slightly.entity.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.Optional;

/**
 * 用户JPA Repository接口
 * 
 * 提供用户相关的数据库操作，继承自JpaRepository
 * 包含自定义查询方法和Spring Data JPA的通用CRUD操作
 */
@Repository
public interface UserRepository extends JpaRepository<User, Long> {

    /**
     * 根据用户名查询用户
     * 
     * @param username 用户名
     * @return 用户实体Optional
     */
    Optional<User> findByUsername(String username);



    /**
     * 根据手机号查询用户
     * 
     * @param phone 手机号
     * @return 用户实体Optional
     */
    Optional<User> findByPhone(String phone);

    /**
     * 检查用户名是否存在
     * 
     * @param username 用户名
     * @return 存在返回true，不存在返回false
     */
    boolean existsByUsername(String username);



    /**
     * 检查手机号是否存在
     * 
     * @param phone 手机号
     * @return 存在返回true，不存在返回false
     */
    boolean existsByPhone(String phone);

    /**
     * 更新用户登录信息
     * 
     * @param userId 用户ID
     * @param loginIp 登录IP
     * @param loginTime 登录时间
     * @return 更新结果
     */
    @Modifying
    @Query("UPDATE User u SET u.lastLoginIp = :loginIp, u.lastLoginTime = :loginTime, u.loginCount = u.loginCount + 1 WHERE u.id = :userId")
    int updateLoginInfo(@Param("userId") Long userId, 
                       @Param("loginIp") String loginIp, 
                       @Param("loginTime") LocalDateTime loginTime);

    /**
     * 更新用户密码
     * 
     * @param userId 用户ID
     * @param newPassword 新密码（已加密）
     * @return 更新结果
     */
    @Modifying
    @Query("UPDATE User u SET u.password = :newPassword WHERE u.id = :userId")
    int updatePassword(@Param("userId") Long userId, 
                      @Param("newPassword") String newPassword);

    /**
     * 统计用户数量（只统计未删除的用户）
     * 
     * @return 用户总数
     */
    @Query("SELECT COUNT(u) FROM User u WHERE u.deleted = 0")
    long countUsers();

    /**
     * 根据状态统计用户数量（只统计未删除的用户）
     * 
     * @param status 用户状态
     * @return 该状态的用户数量
     */
    @Query("SELECT COUNT(u) FROM User u WHERE u.status = :status AND u.deleted = 0")
    long countUsersByStatus(@Param("status") Integer status);
}