package com.ywcx.cx_ai_slightly.websocket;

import io.netty.bootstrap.ServerBootstrap;
import io.netty.channel.ChannelFuture;
import io.netty.channel.ChannelOption;
import io.netty.channel.EventLoopGroup;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.nio.NioServerSocketChannel;
import io.netty.handler.logging.LogLevel;
import io.netty.handler.logging.LoggingHandler;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class NettyServer {

    @Value("${netty.port}")
    private int port;

    @Autowired
    private NettyServerInitializer nettyServerInitializer;

    // boss线程组使用固定线程数
    private EventLoopGroup bossGroup = new NioEventLoopGroup(1);
    // worker线程组使用默认线程数（Netty默认为CPU核心数 * 2）
    private EventLoopGroup workerGroup = new NioEventLoopGroup();
    private ChannelFuture channelFuture;

    @PostConstruct
    public void start() throws InterruptedException {
        log.info("正在启动Netty WebSocket服务器...");

        ServerBootstrap bootstrap = new ServerBootstrap();
        bootstrap.group(bossGroup, workerGroup)
                .channel(NioServerSocketChannel.class)
                .handler(new LoggingHandler(LogLevel.INFO)) // 为服务器socket添加日志记录
                .childHandler(nettyServerInitializer)
                .option(ChannelOption.SO_BACKLOG, 128) // 设置连接请求队列的最大长度
                .childOption(ChannelOption.SO_KEEPALIVE, true); // 保持连接活跃

        // 绑定端口并开始接受连接。
        channelFuture = bootstrap.bind(port).sync();

        channelFuture.channel().closeFuture().addListener(future -> {
            log.info("Netty WebSocket服务器通道已关闭。");
        });

        if (channelFuture.isSuccess()) {
            log.info("Netty WebSocket服务器成功启动，端口: {}", port);
        } else {
            log.error("Netty WebSocket服务器启动失败，端口: {}。", port);
            // 启动失败时，关闭线程组
            stop();
        }
    }

    @PreDestroy
    public void stop() {
        log.info("正在关闭Netty WebSocket服务器...");
        try {
            if (channelFuture != null) {
                channelFuture.channel().close().syncUninterruptibly();
            }
        } finally {
            // 关闭所有事件循环以终止所有线程。
            if (bossGroup != null && !bossGroup.isShuttingDown()) {
                bossGroup.shutdownGracefully(0, 5, TimeUnit.SECONDS).syncUninterruptibly();
            }
            if (workerGroup != null && !workerGroup.isShuttingDown()) {
                workerGroup.shutdownGracefully(0, 5, TimeUnit.SECONDS).syncUninterruptibly();
            }
        }
        log.info("Netty WebSocket服务器已优雅关闭。");
    }
}
