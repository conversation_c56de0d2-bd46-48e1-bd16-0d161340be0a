package com.ywcx.cx_ai_slightly.websocket;

import io.netty.channel.ChannelInitializer;
import io.netty.channel.ChannelPipeline;
import io.netty.channel.socket.SocketChannel;
import io.netty.handler.codec.http.HttpObjectAggregator;
import io.netty.handler.codec.http.HttpServerCodec;
import io.netty.handler.codec.http.websocketx.WebSocketServerProtocolHandler;
import io.netty.handler.stream.ChunkedWriteHandler;
import io.netty.handler.timeout.ReadTimeoutHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

@Component
public class NettyServerInitializer extends ChannelInitializer<SocketChannel> {

    private final WebSocketFrameHandler webSocketFrameHandler;

    @Autowired
    public NettyServerInitializer(WebSocketFrameHandler webSocketFrameHandler) {
        this.webSocketFrameHandler = webSocketFrameHandler;
    }

    @Override
    protected void initChannel(SocketChannel ch) throws Exception {
        ChannelPipeline pipeline = ch.pipeline();

        // 添加处理器以关闭空闲连接。
        pipeline.addLast(new ReadTimeoutHandler(60, TimeUnit.SECONDS));

        // HTTP相关处理器
        pipeline.addLast(new HttpServerCodec());
        pipeline.addLast(new ChunkedWriteHandler());
        pipeline.addLast(new HttpObjectAggregator(65536));

        // WebSocket协议处理器
        pipeline.addLast(new WebSocketServerProtocolHandler("/ws"));

        // 自定义应用逻辑
        pipeline.addLast(webSocketFrameHandler);
    }
}
