package com.ywcx.cx_ai_slightly.websocket;

import com.alibaba.nls.client.AccessToken;
import com.alibaba.nls.client.protocol.InputFormatEnum;
import com.alibaba.nls.client.protocol.NlsClient;
import com.alibaba.nls.client.protocol.SampleRateEnum;
import com.alibaba.nls.client.protocol.asr.SpeechTranscriber;
import com.alibaba.nls.client.protocol.asr.SpeechTranscriberListener;
import com.alibaba.nls.client.protocol.asr.SpeechTranscriberResponse;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.ywcx.cx_ai_slightly.service.AiChatService;
import com.ywcx.cx_ai_slightly.utils.TTSUtil;
import io.micrometer.common.util.StringUtils;
import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandler.Sharable;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.SimpleChannelInboundHandler;
import io.netty.handler.codec.http.websocketx.BinaryWebSocketFrame;
import io.netty.handler.codec.http.websocketx.TextWebSocketFrame;
import io.netty.handler.codec.http.websocketx.WebSocketFrame;
import io.netty.util.AttributeKey;
import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

import java.io.IOException;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicReference;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@Component
@Sharable
public class WebSocketFrameHandler extends SimpleChannelInboundHandler<WebSocketFrame> {

    // Inner class for TTS Task data structure
    private static class TtsTask {
        private final long sequence;
        private final String text;
        private final CompletableFuture<String> future;

        public TtsTask(long sequence, String text, CompletableFuture<String> future) {
            this.sequence = sequence;
            this.text = text;
            this.future = future;
        }

        public long getSequence() {
            return sequence;
        }

        public CompletableFuture<String> getFuture() {
            return future;
        }
    }

    // Inner class for managing TTS task queue per connection
    @Slf4j
    private static class TtsQueueManager {
        private final ChannelHandlerContext ctx;
        private final BlockingQueue<TtsTask> taskQueue = new LinkedBlockingQueue<>();
        private final AtomicLong nextSequence = new AtomicLong(0);
        private final AtomicLong nextExpectedSequence = new AtomicLong(0);
        private final ConcurrentHashMap<Long, String> completedTasks = new ConcurrentHashMap<>();
        private final Future<?> processingTask;

        public TtsQueueManager(ChannelHandlerContext ctx, ExecutorService processingExecutor) {
            this.ctx = ctx;
            this.processingTask = processingExecutor.submit(this::processTasks);
        }

        public void addTask(String text, CompletableFuture<String> future) {
            if (processingTask.isDone()) {
                log.warn("通道{}的TTS队列管理器已关闭，不添加新任务。", ctx.channel().id());
                return;
            }
            long sequence = nextSequence.getAndIncrement();
            TtsTask task = new TtsTask(sequence, text, future);
            try {
                taskQueue.put(task);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("无法为通道{}添加TTS任务到队列", ctx.channel().id(), e);
            }
        }

        private void processTasks() {
            while (!Thread.currentThread().isInterrupted()) {
                try {
                    TtsTask task = taskQueue.take();
                    String mp3Url = task.getFuture().get(); // Block until TTS conversion is done

                    // 如果TTS转换失败，mp3Url将为空。不要添加到映射中。
                    if (mp3Url != null) {
                        completedTasks.put(task.getSequence(), mp3Url);
                    } else {
                        // 如果URL为空，任务失败。为防止队列停滞，
                        // 我们必须将这个序列号视为“完成”但没有URL。
                        // 我们可以通过添加占位符然后立即删除来实现，
                        // 实际上只是推进序列。
                        log.warn("序列号{}的TTS任务失败，返回了空URL。跳过。", task.getSequence());
                        completedTasks.put(task.getSequence(), "#SKIPPED#");
                    }
                    sendAvailableTasks();
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.warn("通道{}的TTS队列处理被中断。", ctx.channel().id());
                    break;
                } catch (Exception e) {
                    if (e instanceof ExecutionException) {
                        log.error("通道{}的TTS转换过程中出现异常", ctx.channel().id(), e.getCause());
                    } else {
                        log.error("通道{}的TTS任务处理出错", ctx.channel().id(), e);
                    }
                    // 即使出现错误，我们也必须推进队列。我们将任务视为已跳过。
                    // This part is tricky. For now, we log and the loop continues. A more robust solution
                    // might involve advancing the sequence number here as well.
                }
            }
        }

        private void sendAvailableTasks() {
            while (ctx.channel().isActive()) {
                long expectedSeq = nextExpectedSequence.get();
                String mp3Url = completedTasks.remove(expectedSeq);

                if (mp3Url != null) {
                    // 如果URL是被跳过任务的占位符，则不发送它。
                    if (!"#SKIPPED#".equals(mp3Url)) {
                        ctx.channel().writeAndFlush(new TextWebSocketFrame("TTS_URL:" + mp3Url));
                    }
                    // 始终推进成功和跳过任务的序列号。
                    nextExpectedSequence.incrementAndGet();
                } else {
                    // No more tasks in sequence are ready.
                    break;
                }
            }
        }

        public void shutdown() {
            log.info("正在关闭通道{}的TTS队列管理器...", ctx.channel().id());
            processingTask.cancel(true); // 中断处理线程
            taskQueue.clear();
            completedTasks.clear();
            log.info("通道{}的TTS队列管理器已关闭。", ctx.channel().id());
        }
    }

    private static final AttributeKey<SpeechTranscriber> ASR_TRANSCRIBER = AttributeKey.valueOf("asrTranscriber");
    private static final AttributeKey<NlsClient> NLS_CLIENT = AttributeKey.valueOf("nlsClient");
    private static final ObjectMapper jsonMapper = new ObjectMapper();
    private static final Pattern SENTENCE_PATTERN = Pattern.compile("([^。？！.!?]*[。？！.!?)])");

    private static final ConcurrentHashMap<String, AtomicReference<StringBuilder>> TEXT_ACCUMULATORS = new ConcurrentHashMap<>();
    private static final ConcurrentHashMap<String, TtsQueueManager> TTS_QUEUE_MANAGERS = new ConcurrentHashMap<>();

    // Shared thread pools for all connections
    private final ExecutorService ttsConversionExecutor = Executors.newCachedThreadPool();
    private final ExecutorService ttsQueueExecutor = Executors.newCachedThreadPool();

    @Value("${aliyun.nls.app-key}")
    private String appKey;
    @Value("${aliyun.nls.access-key-id}")
    private String accessKeyId;
    @Value("${aliyun.nls.access-key-secret}")
    private String accessKeySecret;
    @Value("${aliyun.nls.gateway-url}")
    private String nlsUrl;

    @Autowired
    private TTSUtil ttsUtil;

    @Autowired
    private AiChatService aiChatService;

    @Override
    protected void channelRead0(ChannelHandlerContext ctx, WebSocketFrame frame) throws Exception {
        if (frame instanceof TextWebSocketFrame) {
            handleTextMessage(ctx, (TextWebSocketFrame) frame);
        } else if (frame instanceof BinaryWebSocketFrame) {
            handleBinaryMessage(ctx, (BinaryWebSocketFrame) frame);
        }
    }

    private void handleTextMessage(ChannelHandlerContext ctx, TextWebSocketFrame textFrame) {
        String message = textFrame.text();
        log.info("从客户端{}接收到消息: {}", ctx.channel().id(), message);
        
        try {
            // 尝试解析为JSON
            ObjectNode jsonNode = jsonMapper.readValue(message, ObjectNode.class);
            
            // 检查是否是包含userid和音频流的JSON
            if (jsonNode.has("userid") && jsonNode.has("audio")) {
                handleJsonMessage(ctx, jsonNode);
            } else {
                // 处理其他文本命令
                String command = message.toUpperCase();
                switch (command) {
                    case "START":
                        startTranscription(ctx);
                        break;
                    case "STOP":
                        stopTranscription(ctx);
                        break;
                    default:
                        ctx.channel().writeAndFlush(new TextWebSocketFrame("服务器收到文本: " + command));
                        break;
                }
            }
        } catch (Exception e) {
            log.error("解析客户端{}的消息失败: {}", ctx.channel().id(), message, e);
            ctx.channel().writeAndFlush(new TextWebSocketFrame("ERROR: 无法解析消息")); 
        }
    }

    private void handleBinaryMessage(ChannelHandlerContext ctx, BinaryWebSocketFrame binaryFrame) {
        SpeechTranscriber transcriber = ctx.channel().attr(ASR_TRANSCRIBER).get();
        if (transcriber != null) {
            ByteBuf content = binaryFrame.content();
            byte[] audioData = new byte[content.readableBytes()];
            content.readBytes(audioData);
            try {
                transcriber.send(audioData);
                log.debug("为通道{}发送了{}字节的音频数据", audioData.length, ctx.channel().id());
            } catch (Exception e) {
                log.error("为通道{}发送音频数据到ASR服务时出错", ctx.channel().id(), e);
            }
        } else {
            log.warn("从通道{}收到音频数据，但没有活跃的语音识别器。", ctx.channel().id());
        }
    }
    
    private void handleJsonMessage(ChannelHandlerContext ctx, ObjectNode jsonNode) {
        // 验证必需字段是否存在
        if (!jsonNode.has("userid") || !jsonNode.has("audio")) {
            String errorMsg = "缺少必需字段";
            if (!jsonNode.has("userid")) {
                errorMsg = "缺少userid字段";
            } else if (!jsonNode.has("audio")) {
                errorMsg = "缺少音频流字段";
            }
            
            log.error("从通道{}接收到的JSON数据无效: {}", ctx.channel().id(), errorMsg);
            ctx.channel().writeAndFlush(new TextWebSocketFrame("ERROR: " + errorMsg));
            return;
        }
        
        String userid = jsonNode.get("userid").asText();
        String chatid = jsonNode.has("chatid") ? jsonNode.get("chatid").asText() : null;
        String audioBase64 = jsonNode.get("audio").asText();
        
        log.info("从通道{}接收到JSON数据，User ID: {}, Chat ID: {}, 音频数据长度: {}", 
                ctx.channel().id(), userid, (chatid != null ? chatid : "未提供"), audioBase64.length());
        
        SpeechTranscriber transcriber = ctx.channel().attr(ASR_TRANSCRIBER).get();
        if (transcriber != null) {
            try {
                // 将Base64编码的音频数据解码为字节数组
                byte[] audioData = java.util.Base64.getDecoder().decode(audioBase64);
                
                // 将音频数据发送到ASR服务
                transcriber.send(audioData);
                log.debug("为通道{}、User ID {}和Chat ID {}发送了{}字节的音频数据", 
                        ctx.channel().id(), userid, (chatid != null ? chatid : "未提供"), audioData.length);
            } catch (Exception e) {
                log.error("为通道{}、User ID {}和Chat ID {}处理音频数据时出错", 
                        ctx.channel().id(), userid, (chatid != null ? chatid : "未提供"), e);
                ctx.channel().writeAndFlush(new TextWebSocketFrame("ERROR: 处理音频数据失败")); 
            }
        } else {
            log.warn("从通道{}、User ID {}和Chat ID {}收到音频数据，但没有活跃的语音识别器。", 
                    ctx.channel().id(), userid, (chatid != null ? chatid : "未提供"));
            ctx.channel().writeAndFlush(new TextWebSocketFrame("ERROR: 语音识别器未启动")); 
        }
    }

    private void startTranscription(ChannelHandlerContext ctx) {
        log.info("为通道{}启动语音识别", ctx.channel().id());
        try {
            AccessToken accessToken = new AccessToken(accessKeyId, accessKeySecret);
            accessToken.apply();
            String token = accessToken.getToken();
            NlsClient client = new NlsClient(nlsUrl, token);
            ctx.channel().attr(NLS_CLIENT).set(client);

            SpeechTranscriberListener listener = createTranscriberListener(ctx);
            SpeechTranscriber transcriber = new SpeechTranscriber(client, listener);
            transcriber.setAppKey(appKey);
            transcriber.setFormat(InputFormatEnum.PCM);
            transcriber.setSampleRate(SampleRateEnum.SAMPLE_RATE_16K);
            transcriber.setEnableIntermediateResult(true);
            transcriber.setEnablePunctuation(true);
            transcriber.setEnableITN(false);

            ctx.channel().attr(ASR_TRANSCRIBER).set(transcriber);
            transcriber.start();
            ctx.channel().writeAndFlush(new TextWebSocketFrame("ASR_STARTED"));
        } catch (Exception e) {
            log.error("为通道{}启动识别失败", ctx.channel().id(), e);
            ctx.channel().writeAndFlush(new TextWebSocketFrame("ASR_ERROR: 启动识别失败。"));
        }
    }

    private void stopTranscription(ChannelHandlerContext ctx) {
        log.info("为通道{}停止语音识别", ctx.channel().id());
        SpeechTranscriber transcriber = ctx.channel().attr(ASR_TRANSCRIBER).getAndSet(null);
        if (transcriber != null) {
            try {
                transcriber.stop();
                transcriber.close();
            } catch (Exception e) {
                log.error("为通道{}停止ASR识别器时出错", ctx.channel().id(), e);
            }
        }

        NlsClient client = ctx.channel().attr(NLS_CLIENT).getAndSet(null);
        if (client != null) {
            client.shutdown();
        }
    }

    private SpeechTranscriberListener createTranscriberListener(ChannelHandlerContext ctx) {
        return new SpeechTranscriberListener() {
            @Override
            public void onTranscriptionResultChange(SpeechTranscriberResponse response) {
                if (StringUtils.isNotBlank(response.getTransSentenceText())) {
                    log.debug("通道{}的中间识别结果: {}", ctx.channel().id(), response.getTransSentenceText());
                    sendJsonResult(ctx, "INTERMEDIATE_RESULT", response);
                }
            }

            @Override
            public void onTranscriberStart(SpeechTranscriberResponse speechTranscriberResponse) {

            }

            @Override
            public void onSentenceBegin(SpeechTranscriberResponse speechTranscriberResponse) {

            }

            @Override
            public void onSentenceEnd(SpeechTranscriberResponse response) {
                String resultText = response.getTransSentenceText();
                log.info("通道{}的句子结束结果: {}", ctx.channel().id(), resultText);
                sendJsonResult(ctx, "SENTENCE_END", response);
                if (StringUtils.isNotBlank(resultText)) {
                    // 从通道中获取userid和chatid
                    String userid = getUserIdFromChannel(ctx);
                    String chatid = getChatIdFromChannel(ctx);
                    sendModel(resultText, ctx, userid, chatid);
                }
            }

            @Override
            public void onTranscriptionComplete(SpeechTranscriberResponse response) {
                log.info("通道{}的识别完成: task_id={}", ctx.channel().id(), response.getTaskId());
                ctx.channel().writeAndFlush(new TextWebSocketFrame("ASR_STOPPED"));
            }

            @Override
            public void onFail(SpeechTranscriberResponse response) {
                log.error("通道{}的识别失败: status={}, text={}",
                        ctx.channel().id(), response.getStatus(), response.getStatusText());
                ctx.channel().writeAndFlush(new TextWebSocketFrame("ASR_ERROR:" + response.getStatusText()));
            }
        };
    }

    private void sendJsonResult(ChannelHandlerContext ctx, String type, SpeechTranscriberResponse response) {
        try {
            ObjectNode jsonResult = jsonMapper.createObjectNode();
            jsonResult.put("index", response.getTransSentenceIndex());
            jsonResult.put("text", response.getTransSentenceText());
            jsonResult.put("time", response.getTransSentenceTime());
            if ("SENTENCE_END".equals(type)) {
                jsonResult.put("begin_time", response.getSentenceBeginTime());
                jsonResult.put("confidence", response.getConfidence());
            }
            ctx.channel().writeAndFlush(new TextWebSocketFrame(type + ":" + jsonMapper.writeValueAsString(jsonResult)));
        } catch (IOException e) {
            log.error("为通道{}将识别结果序列化为JSON失败", ctx.channel().id(), e);
        }
    }

    @Override
    public void handlerAdded(ChannelHandlerContext ctx) {
        String channelId = ctx.channel().id().asLongText();
        log.info("客户端已连接: {}。正在初始化资源。", channelId);
        TEXT_ACCUMULATORS.computeIfAbsent(channelId, k -> new AtomicReference<>(new StringBuilder()));
        TTS_QUEUE_MANAGERS.computeIfAbsent(channelId, k -> new TtsQueueManager(ctx, ttsQueueExecutor));
    }

    @Override
    public void handlerRemoved(ChannelHandlerContext ctx) {
        String channelId = ctx.channel().id().asLongText();
        log.info("客户端已断开连接: {}。正在清理资源。", channelId);
        TEXT_ACCUMULATORS.remove(channelId);
        TtsQueueManager queueManager = TTS_QUEUE_MANAGERS.remove(channelId);
        if (queueManager != null) {
            queueManager.shutdown();
        }
        stopTranscription(ctx);
    }

    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) {
        log.error("通道{}WebSocket处理器中捕获到异常", ctx.channel().id(), cause);
        ctx.close(); // 这会触发handlerRemoved以清理资源
    }

    @PreDestroy
    public void shutdown() {
        log.info("正在关闭WebSocketFrameHandler及其线程池。");
        ttsConversionExecutor.shutdownNow();
        ttsQueueExecutor.shutdownNow();
    }

    public void sendModel(String query, ChannelHandlerContext ctx, String userid, String chatid) {
        String channelId = ctx.channel().id().asLongText();
        log.info("为通道{}、用户{}和聊天{}发送模型查询: {}", channelId, userid, (chatid != null ? chatid : "未提供"), query);
        Flux<String> stringFlux = aiChatService.streamingChat(query);

        stringFlux.subscribe(
                content -> processAiContent(content, channelId, ctx, userid, chatid),
                error -> {
                    log.error("通道{}、用户{}和聊天{}的流式聊天中出错", channelId, userid, (chatid != null ? chatid : "未提供"), error);
                    handleRemainingText(ctx, channelId, userid, chatid); // 尝试处理任何剩余的文本
                },
                () -> {
                    log.info("通道{}、用户{}和聊天{}的流式聊天完成", channelId, userid, (chatid != null ? chatid : "未提供"));
                    handleRemainingText(ctx, channelId, userid, chatid); // 处理最后一块文本
                }
        );
    }

    private void processAiContent(String content, String channelId, ChannelHandlerContext ctx, String userid, String chatid) {
        if (StringUtils.isBlank(content)) return;

        AtomicReference<StringBuilder> accumulatorRef = TEXT_ACCUMULATORS.get(channelId);
        if (accumulatorRef == null) {
            log.warn("已断开连接的通道{}找不到文本累加器。忽略内容。", channelId);
            return;
        }

        StringBuilder accumulator = accumulatorRef.get();
        accumulator.append(content);

        Matcher matcher = SENTENCE_PATTERN.matcher(accumulator.toString());

        int lastEnd = 0;
        while (matcher.find()) {
            String sentence = matcher.group().trim();
            if (!sentence.isEmpty()) {
                log.info("为通道{}分发句子到TTS: {}", channelId, sentence);
                processTtsTask(sentence, channelId, ctx);
            }
            lastEnd = matcher.end();
        }

        if (lastEnd > 0) {
            accumulator.delete(0, lastEnd);
        }
    }

    private void processTtsTask(String sentence, String channelId, ChannelHandlerContext ctx, String userid, String chatid) {
        TtsQueueManager queueManager = TTS_QUEUE_MANAGERS.get(channelId);
        if (queueManager == null) {
            log.warn("已断开连接的通道{}找不到TTS队列管理器。忽略TTS任务。", channelId);
            return;
        }

        CompletableFuture<String> ttsFuture = CompletableFuture.supplyAsync(() -> {
            try {
                return ttsUtil.sendTts(sentence);
            } catch (IOException e) {
                log.error("通道{}、用户{}和聊天{}上的句子'{}'的TTS转换失败", channelId, userid, (chatid != null ? chatid : "未提供"), sentence, e);
                throw new RuntimeException("语音合成失败", e);
            }
        }, ttsConversionExecutor);

        queueManager.addTask(sentence, ttsFuture);
    }

    private void handleRemainingText(ChannelHandlerContext ctx, String channelId, String userid, String chatid) {
        AtomicReference<StringBuilder> accumulatorRef = TEXT_ACCUMULATORS.get(channelId);
        if (accumulatorRef != null) {
            String remainingText = accumulatorRef.get().toString().trim();
            if (!remainingText.isEmpty()) {
                log.info("为通道{}、用户{}和聊天{}分发剩余文本到TTS: {}", channelId, userid, (chatid != null ? chatid : "未提供"), remainingText);
                processTtsTask(remainingText, channelId, ctx, userid, chatid);
            }
            accumulatorRef.get().setLength(0);
        }
    }
}