package com.ywcx.cx_ai_slightly.interceptor;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ywcx.cx_ai_slightly.common.Result;
import com.ywcx.cx_ai_slightly.service.AntiCrawlerService;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import java.io.IOException;

/**
 * 反爬虫拦截器
 */
@Slf4j
@Component
public class AntiCrawlerInterceptor implements HandlerInterceptor {
    
    @Autowired
    private AntiCrawlerService antiCrawlerService;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 记录请求
        antiCrawlerService.recordRequest(request);
        
        // 检查请求是否被允许
        if (!antiCrawlerService.isRequestAllowed(request)) {
            handleRejectedRequest(request, response);
            return false;
        }
        
        return true;
    }
    
    /**
     * 处理被拒绝的请求
     */
    private void handleRejectedRequest(HttpServletRequest request, HttpServletResponse response) throws IOException {
        String clientIp = antiCrawlerService.getClientIp(request);
        log.warn("拒绝来自 {} 的请求: {}", clientIp, request.getRequestURI());
        
        response.setStatus(429); // HTTP 429 Too Many Requests
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        response.setCharacterEncoding("UTF-8");
        
        // 使用统一的Result返回格式
        Result<Object> result = Result.error(429, "请求过于频繁，请稍后再试");
        
        response.getWriter().write(objectMapper.writeValueAsString(result));
    }
}
