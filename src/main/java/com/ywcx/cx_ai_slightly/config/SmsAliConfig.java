package com.ywcx.cx_ai_slightly.config;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.dysmsapi20170525.Client;
import com.aliyun.dysmsapi20170525.models.SendSmsRequest;
import com.aliyun.dysmsapi20170525.models.SendSmsResponse;
import com.aliyun.teaopenapi.models.Config;
import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;



/**
 * <AUTHOR>
 * @create 2025-09-03
 */

@Component
public class SmsAliConfig {

    @Value("${aliyun.nls.access-key-id}")
    private String accessKey;

    @Value("${aliyun.nls.access-key-secret}")
    private String secretKey;

    private Client client;

    private static SmsAliConfig instance;

    @PostConstruct
    public void init() throws Exception {
        this.client = createClient();
        instance = this;
    }

    Client createClient() throws Exception {
        Config config = new Config()
                // 配置 AccessKey ID
                .setAccessKeyId(accessKey)
                // 配置 AccessKey Secret
                .setAccessKeySecret(secretKey);

        // 配置 Endpoint。中国站请使用dysmsapi.aliyuncs.com
        config.endpoint = "dysmsapi.aliyuncs.com";

        return new Client(config);
    }

    public void sendSms(String phone, String code) {
        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("code", code);
            String jsonString = jsonObject.toJSONString();
            SendSmsRequest sendSmsRequest = new SendSmsRequest()
                    .setPhoneNumbers(phone)
                    .setSignName("仰望晨星北京科技")
                    .setTemplateCode("SMS_493230394")
                    .setTemplateParam(jsonString);

            client.sendSms(sendSmsRequest);
        } catch (Exception e) {
            // 处理异常，可以记录日志或抛出运行时异常
            throw new RuntimeException("发送短信失败", e);
        }
    }

    // 提供静态方法供其他类调用
    public static void sendSmsMessage(String phone, String code) {
        if (instance == null) {
            throw new IllegalStateException("SmsAliConfig未初始化");
        }
        instance.sendSms(phone, code);
    }
}
