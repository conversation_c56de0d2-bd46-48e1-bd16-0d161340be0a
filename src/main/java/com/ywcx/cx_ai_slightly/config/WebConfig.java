package com.ywcx.cx_ai_slightly.config;

import com.ywcx.cx_ai_slightly.interceptor.AntiCrawlerInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Web配置类
 * 
 * 配置Web相关的拦截器和其他Web组件
 * 主要功能：
 * - 注册反爬虫拦截器
 * - 设置拦截器的执行顺序和路径匹配规则
 */
@Configuration
public class WebConfig implements WebMvcConfigurer {
    
    @Autowired
    private AntiCrawlerInterceptor antiCrawlerInterceptor;
    
    /**
     * 注册拦截器
     * 
     * 配置反爬虫拦截器，确保其在Sa-Token拦截器之前执行
     * 
     * @param registry 拦截器注册器
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 注册反爬虫拦截器
        registry.addInterceptor(antiCrawlerInterceptor)
                .addPathPatterns("/api/**") // 拦截所有API请求
                .excludePathPatterns(
                        "/api/captcha/**",     // 排除验证码接口
                        "/swagger-ui/**",      // 排除Swagger UI
                        "/v3/api-docs/**",     // 排除API文档
                        "/actuator/**"         // 排除监控端点
                )
                .order(-1); // 设置最高优先级，确保在Sa-Token拦截器之前执行
    }
}