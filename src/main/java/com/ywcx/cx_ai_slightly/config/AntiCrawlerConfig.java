package com.ywcx.cx_ai_slightly.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * 反爬虫配置
 */
@Configuration
@ConfigurationProperties(prefix = "anti-crawler")
public class AntiCrawlerConfig {
    
    /**
     * 是否启用反爬虫
     */
    private boolean enabled = true;
    
    /**
     * 请求频率限制配置
     */
    private RateLimit rateLimit = new RateLimit();
    
    /**
     * 黑名单User-Agent
     */
    private List<String> blacklistUserAgents = List.of(
        "python-requests",
        "curl",
        "wget",
        "scrapy",
        "bot",
        "spider",
        "crawler"
    );
    
    /**
     * IP黑名单
     */
    private List<String> blacklistIps = List.of();
    
    /**
     * 白名单IP（不受限制）
     */
    private List<String> whitelistIps = List.of(
        "127.0.0.1",
        "localhost"
    );
    
    /**
     * 需要验证码的接口路径
     */
    private List<String> captchaRequiredPaths = List.of(
        "/api/auth/login",
        "/api/auth/register"
    );
    
    public static class RateLimit {
        /**
         * 时间窗口（秒）
         */
        private int windowSeconds = 60;
        
        /**
         * 最大请求次数
         */
        private int maxRequests = 100;
        
        /**
         * 封禁时间（秒）
         */
        private int banDurationSeconds = 300;
        
        // getters and setters
        public int getWindowSeconds() {
            return windowSeconds;
        }
        
        public void setWindowSeconds(int windowSeconds) {
            this.windowSeconds = windowSeconds;
        }
        
        public int getMaxRequests() {
            return maxRequests;
        }
        
        public void setMaxRequests(int maxRequests) {
            this.maxRequests = maxRequests;
        }
        
        public int getBanDurationSeconds() {
            return banDurationSeconds;
        }
        
        public void setBanDurationSeconds(int banDurationSeconds) {
            this.banDurationSeconds = banDurationSeconds;
        }
    }
    
    // getters and setters
    public boolean isEnabled() {
        return enabled;
    }
    
    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }
    
    public RateLimit getRateLimit() {
        return rateLimit;
    }
    
    public void setRateLimit(RateLimit rateLimit) {
        this.rateLimit = rateLimit;
    }
    
    public List<String> getBlacklistUserAgents() {
        return blacklistUserAgents;
    }
    
    public void setBlacklistUserAgents(List<String> blacklistUserAgents) {
        this.blacklistUserAgents = blacklistUserAgents;
    }
    
    public List<String> getBlacklistIps() {
        return blacklistIps;
    }
    
    public void setBlacklistIps(List<String> blacklistIps) {
        this.blacklistIps = blacklistIps;
    }
    
    public List<String> getWhitelistIps() {
        return whitelistIps;
    }
    
    public void setWhitelistIps(List<String> whitelistIps) {
        this.whitelistIps = whitelistIps;
    }
    
    public List<String> getCaptchaRequiredPaths() {
        return captchaRequiredPaths;
    }
    
    public void setCaptchaRequiredPaths(List<String> captchaRequiredPaths) {
        this.captchaRequiredPaths = captchaRequiredPaths;
    }
}