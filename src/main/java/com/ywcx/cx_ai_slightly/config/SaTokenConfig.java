package com.ywcx.cx_ai_slightly.config;

import cn.dev33.satoken.interceptor.SaInterceptor;
import cn.dev33.satoken.router.SaRouter;
import cn.dev33.satoken.stp.StpUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Sa-Token权限认证配置类
 * 
 * 配置Sa-Token的拦截器和路由规则，实现统一的权限控制
 * 
 * 主要功能：
 * - 配置全局拦截器，对所有请求进行权限检查
 * - 设置白名单，允许特定路径无需认证即可访问
 * - 提供灵活的路由匹配规则
 */
@Configuration
@Slf4j
public class SaTokenConfig implements WebMvcConfigurer {
    
    /**
     * 注册Sa-Token拦截器
     * 
     * 配置全局拦截器，对所有请求进行权限验证
     * 除白名单中的路径外，其他所有路径都需要登录后才能访问
     * 
     * @param registry 拦截器注册器
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 注册Sa-Token拦截器，校验规则为 StpUtil.checkLogin() 登录校验
        registry.addInterceptor(new SaInterceptor(handler -> {
            // 开发阶段，放开所有接口
            SaRouter.match("/**").check(r -> {});
        }))
        // 拦截所有路径
        .addPathPatterns("/**")
        // 设置拦截器优先级
        .order(1);
        
        log.info("Sa-Token拦截器已配置，当前为开发模式，所有接口均已放行。");
    }
    
    /**
     * 获取权限验证白名单
     * 
     * 白名单中的路径不需要登录即可访问
     * 包括：认证相关接口、静态资源、健康检查、API文档等
     * 
     * @return String[] 白名单路径数组
     */
    private String[] getWhiteList() {
        return new String[]{
            // =============================认证相关接口=============================
            "/api/auth/login",          // 用户登录接口
            "/api/auth/register",       // 用户注册接口
            "/api/auth/sms-login",      // 短信登录/注册接口
            "/api/auth/logout",         // 用户登出接口
            
            // =============================健康检查接口=============================
            "/api/health",              // 健康检查接口
            "/health",                  // 健康检查接口（备用）
            
            // =============================监控相关=============================
            "/actuator/**",             // Spring Boot Actuator监控端点
            
            // =============================API文档相关=============================
            "/swagger-ui/**",           // Swagger UI界面
            "/v3/api-docs/**",          // OpenAPI 3.0文档
            "/swagger-resources/**",    // Swagger资源
            "/webjars/**",              // WebJars静态资源
            "/doc.html",                // Knife4j文档页面
            "/api/swagger-ui/**",       // Swagger UI界面（带上下文路径）
            "/api/v3/api-docs/**",      // OpenAPI 3.0文档（带上下文路径）
            "/api/swagger-ui.html",     // Swagger UI首页（带上下文路径）
            
            // =============================静态资源=============================
            "/static/**",               // 静态资源目录
            "/css/**",                  // CSS样式文件
            "/js/**",                   // JavaScript文件
            "/images/**",               // 图片资源
            "/favicon.ico",             // 网站图标
            "/robots.txt",              // 搜索引擎爬虫规则
            
            // =============================错误处理=============================
            "/error",                   // 错误页面
            "/error/**",                // 错误处理相关
            
            // =============================开发调试=============================
            "/test/**",                 // 测试相关接口（开发环境）
            "/debug/**",                // 调试相关接口（开发环境）
            "/api/test/**",             // API测试接口
            "/api/captcha/**",          // 验证码接口
            
            // =============================其他=============================
            "/",                        // 根路径
            "/index.html"               // 首页
        };
    }
    
    /**
     * 检查路径是否在白名单中
     * 
     * 提供给其他组件使用的工具方法
     * 
     * @param path 请求路径
     * @return boolean true-在白名单中，false-不在白名单中
     */
    public boolean isWhitePath(String path) {
        if (path == null || path.trim().isEmpty()) {
            return false;
        }
        
        String[] whiteList = getWhiteList();
        for (String whitePath : whiteList) {
            // 支持通配符匹配
            if (whitePath.endsWith("/**")) {
                String prefix = whitePath.substring(0, whitePath.length() - 3);
                if (path.startsWith(prefix)) {
                    return true;
                }
            } else if (whitePath.endsWith("/*")) {
                String prefix = whitePath.substring(0, whitePath.length() - 2);
                if (path.startsWith(prefix) && path.indexOf('/', prefix.length()) == -1) {
                    return true;
                }
            } else if (path.equals(whitePath)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 获取白名单路径数量
     * 
     * @return int 白名单路径数量
     */
    public int getWhiteListSize() {
        return getWhiteList().length;
    }
    
    /**
     * 打印白名单信息（用于调试）
     */
    public void printWhiteList() {
        String[] whiteList = getWhiteList();
        log.info("Sa-Token权限验证白名单 (共{}个路径):", whiteList.length);
        for (int i = 0; i < whiteList.length; i++) {
            log.info("  {}: {}", i + 1, whiteList[i]);
        }
    }
}
