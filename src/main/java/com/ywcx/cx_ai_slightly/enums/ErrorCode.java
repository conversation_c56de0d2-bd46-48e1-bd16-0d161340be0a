package com.ywcx.cx_ai_slightly.enums;

/**
 * 错误码枚举
 */
public enum ErrorCode {
    
    SUCCESS(200, "操作成功"),
    SYSTEM_ERROR(1000, "系统内部错误"),
    PARAM_ERROR(1001, "参数错误"),
    NETWORK_ERROR(1002, "网络异常"),
    TIMEOUT_ERROR(1003, "请求超时"),
    
    AI_SERVICE_ERROR(2000, "AI服务异常"),
    AI_API_KEY_ERROR(2001, "AI API密钥错误"),
    AI_MODEL_ERROR(2002, "AI模型错误"),
    AI_QUOTA_EXCEEDED(2003, "AI服务配额已用完"),
    AI_CONTENT_FILTERED(2004, "内容被过滤"),
    
    MESSAGE_EMPTY(3000, "消息内容不能为空"),
    MESSAGE_TOO_LONG(3001, "消息内容过长"),
    CITY_NAME_INVALID(3002, "城市名称无效"),
    
    UNAUTHORIZED(4001, "未授权访问"),
    FORBIDDEN(4003, "禁止访问"),
    
    RESOURCE_NOT_FOUND(5004, "资源未找到");
    
    private final int code;
    private final String message;
    
    ErrorCode(int code, String message) {
        this.code = code;
        this.message = message;
    }
    
    public int getCode() {
        return code;
    }
    
    public String getMessage() {
        return message;
    }
}