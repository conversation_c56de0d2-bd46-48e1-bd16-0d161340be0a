package com.ywcx.cx_ai_slightly.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class HttpClient {
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    private final OkHttpClient client = new OkHttpClient.Builder()
            .connectTimeout(30, TimeUnit.SECONDS)
            .readTimeout(60, TimeUnit.SECONDS)
            .writeTimeout(60, TimeUnit.SECONDS)
            .retryOnConnectionFailure(true)
            .build();
    
    private static final MediaType JSON_MEDIA_TYPE = MediaType.parse("application/json; charset=utf-8");
    private static final MediaType FORM_MEDIA_TYPE = MediaType.parse("application/x-www-form-urlencoded");
    
    @Data
    public static class HttpResponse {
        private int code;
        private String message;
        private String body;
        private Map<String, String> headers;
        private boolean success;
        
        public HttpResponse(int code, String message, String body, Map<String, String> headers) {
            this.code = code;
            this.message = message;
            this.body = body;
            this.headers = headers;
            this.success = code >= 200 && code < 300;
        }
    }
    
    public HttpResponse get(String url, Map<String, String> headers, Map<String, String> params) {
        HttpUrl.Builder urlBuilder = HttpUrl.parse(url).newBuilder();
        
        if (params != null) {
            params.forEach(urlBuilder::addQueryParameter);
        }
        
        Request.Builder requestBuilder = new Request.Builder()
                .url(urlBuilder.build())
                .get();
        
        if (headers != null) {
            headers.forEach(requestBuilder::addHeader);
        }
        
        return executeRequest(requestBuilder.build());
    }
    
    public HttpResponse get(String url) {
        return get(url, null, null);
    }
    
    public HttpResponse postJson(String url, Object body, Map<String, String> headers) {
        try {
            String jsonBody = body instanceof String ? (String) body : objectMapper.writeValueAsString(body);
            RequestBody requestBody = RequestBody.create(jsonBody, JSON_MEDIA_TYPE);
            
            Request.Builder requestBuilder = new Request.Builder()
                    .url(url)
                    .post(requestBody);
            
            if (headers != null) {
                headers.forEach(requestBuilder::addHeader);
            }
            
            return executeRequest(requestBuilder.build());
        } catch (Exception e) {
            log.error("POST JSON请求失败: {}", e.getMessage(), e);
            return new HttpResponse(-1, "请求失败: " + e.getMessage(), null, new HashMap<>());
        }
    }
    
    public HttpResponse postJson(String url, Object body) {
        return postJson(url, body, null);
    }
    
    public HttpResponse postForm(String url, Map<String, String> formData, Map<String, String> headers) {
        FormBody.Builder formBuilder = new FormBody.Builder();
        
        if (formData != null) {
            formData.forEach(formBuilder::add);
        }
        
        Request.Builder requestBuilder = new Request.Builder()
                .url(url)
                .post(formBuilder.build());
        
        if (headers != null) {
            headers.forEach(requestBuilder::addHeader);
        }
        
        return executeRequest(requestBuilder.build());
    }
    
    public HttpResponse postForm(String url, Map<String, String> formData) {
        return postForm(url, formData, null);
    }
    
    public HttpResponse put(String url, Object body, Map<String, String> headers) {
        try {
            String jsonBody = body instanceof String ? (String) body : objectMapper.writeValueAsString(body);
            RequestBody requestBody = RequestBody.create(jsonBody, JSON_MEDIA_TYPE);
            
            Request.Builder requestBuilder = new Request.Builder()
                    .url(url)
                    .put(requestBody);
            
            if (headers != null) {
                headers.forEach(requestBuilder::addHeader);
            }
            
            return executeRequest(requestBuilder.build());
        } catch (Exception e) {
            log.error("PUT请求失败: {}", e.getMessage(), e);
            return new HttpResponse(-1, "请求失败: " + e.getMessage(), null, new HashMap<>());
        }
    }
    
    public HttpResponse put(String url, Object body) {
        return put(url, body, null);
    }
    
    public HttpResponse delete(String url, Map<String, String> headers) {
        Request.Builder requestBuilder = new Request.Builder()
                .url(url)
                .delete();
        
        if (headers != null) {
            headers.forEach(requestBuilder::addHeader);
        }
        
        return executeRequest(requestBuilder.build());
    }
    
    public HttpResponse delete(String url) {
        return delete(url, null);
    }
    
    public HttpResponse patch(String url, Object body, Map<String, String> headers) {
        try {
            String jsonBody = body instanceof String ? (String) body : objectMapper.writeValueAsString(body);
            RequestBody requestBody = RequestBody.create(jsonBody, JSON_MEDIA_TYPE);
            
            Request.Builder requestBuilder = new Request.Builder()
                    .url(url)
                    .patch(requestBody);
            
            if (headers != null) {
                headers.forEach(requestBuilder::addHeader);
            }
            
            return executeRequest(requestBuilder.build());
        } catch (Exception e) {
            log.error("PATCH请求失败: {}", e.getMessage(), e);
            return new HttpResponse(-1, "请求失败: " + e.getMessage(), null, new HashMap<>());
        }
    }
    
    public HttpResponse patch(String url, Object body) {
        return patch(url, body, null);
    }
    
    public HttpResponse uploadFile(String url, String fileParam, String fileName, 
                                 byte[] fileContent, Map<String, String> formData, 
                                 Map<String, String> headers) {
        MultipartBody.Builder multipartBuilder = new MultipartBody.Builder()
                .setType(MultipartBody.FORM);
        
        multipartBuilder.addFormDataPart(fileParam, fileName,
                RequestBody.create(fileContent, MediaType.parse("application/octet-stream")));
        
        if (formData != null) {
            formData.forEach(multipartBuilder::addFormDataPart);
        }
        
        Request.Builder requestBuilder = new Request.Builder()
                .url(url)
                .post(multipartBuilder.build());
        
        if (headers != null) {
            headers.forEach(requestBuilder::addHeader);
        }
        
        return executeRequest(requestBuilder.build());
    }
    
    private HttpResponse executeRequest(Request request) {
        try {
            log.debug("发送HTTP请求: {} {}", request.method(), request.url());
            
            try (Response response = client.newCall(request).execute()) {
                Map<String, String> responseHeaders = new HashMap<>();
                response.headers().forEach(pair -> responseHeaders.put(pair.getFirst(), pair.getSecond()));
                
                String responseBody = response.body() != null ? response.body().string() : null;
                
                log.debug("收到HTTP响应: {} - {}", response.code(), response.message());
                
                return new HttpResponse(response.code(), response.message(), responseBody, responseHeaders);
            }
        } catch (IOException e) {
            log.error("HTTP请求执行失败: {}", e.getMessage(), e);
            return new HttpResponse(-1, "请求执行失败: " + e.getMessage(), null, new HashMap<>());
        } catch (Exception e) {
            log.error("HTTP请求异常: {}", e.getMessage(), e);
            return new HttpResponse(-1, "请求异常: " + e.getMessage(), null, new HashMap<>());
        }
    }
    
    public <T> T parseJson(HttpResponse response, Class<T> clazz) {
        try {
            if (response.isSuccess() && response.getBody() != null && !response.getBody().trim().isEmpty()) {
                return objectMapper.readValue(response.getBody(), clazz);
            }
            return null;
        } catch (Exception e) {
            log.error("JSON解析失败: {}", e.getMessage(), e);
            return null;
        }
    }
    
    public Map<String, String> buildHeaders(String token, String contentType, Map<String, String> additionalHeaders) {
        Map<String, String> headers = new HashMap<>();
        
        if (token != null && !token.trim().isEmpty()) {
            headers.put("Authorization", "Bearer " + token);
        }
        
        if (contentType != null && !contentType.trim().isEmpty()) {
            headers.put("Content-Type", contentType);
        }
        
        headers.put("User-Agent", "HttpClient/1.0");
        
        if (additionalHeaders != null) {
            headers.putAll(additionalHeaders);
        }
        
        return headers;
    }
    
    public Map<String, String> buildHeaders(String token) {
        return buildHeaders(token, null, null);
    }
    
    public Map<String, String> buildHeaders() {
        return buildHeaders(null, null, null);
    }

    /**
     * 远程post请求
     *
     * @param url
     * @param headers
     * @param json
     * @return
     * @throws IOException
     */
    public static JSONObject post(String url, Headers headers, String json) throws IOException {
        OkHttpClient client = new OkHttpClient();
        RequestBody body = RequestBody.create(json, MediaType.get("application/json; charset=utf-8"));
        Request request = new Request.Builder()
                .url(url)
                .post(body)
                .headers(headers)
                .build();
        try (Response response = client.newCall(request).execute()) {
            JSONObject res = JSON.parseObject(response.body().string());
            return res;
        }
    }
}