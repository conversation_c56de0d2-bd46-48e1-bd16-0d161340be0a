package com.ywcx.cx_ai_slightly.utils;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.oss.ClientBuilderConfiguration;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.common.auth.CredentialsProvider;
import com.aliyun.oss.common.auth.DefaultCredentialProvider;
import com.aliyun.oss.common.comm.SignVersion;
import com.aliyun.oss.model.VoidResult;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.io.InputStream;


/**
 * 阿里云 OSS 工具类 (Spring-managed Component)
 *
 * <AUTHOR>
 * @create 2025-09-01
 */
@Slf4j
@Component
public class AliOssUtil {

    @Value("${aliyun.oss.endpoint:https://oss-cn-beijing.aliyuncs.com}")
    private String endpoint;

    @Value("${aliyun.oss.bucket-name:ywcx-ai-bot}")
    private String bucketName;

    @Value("${aliyun.nls.access-key-id}")
    private String accessKey;

    @Value("${aliyun.nls.access-key-secret}")
    private String secretKey;

    private OSS ossClient;

    @PostConstruct
    public void init() {
        try {
            CredentialsProvider credentialsProvider = new DefaultCredentialProvider(accessKey, secretKey);
            String region = "cn-beijing";

            ClientBuilderConfiguration conf = new ClientBuilderConfiguration();
            conf.setSignatureVersion(SignVersion.V4);
            ossClient = OSSClientBuilder.create().endpoint(endpoint).credentialsProvider(credentialsProvider).region(region).build();

            log.info("OSS client initialized successfully.");
        } catch (Exception e) {
            log.error("Failed to initialize OSS client", e);
            throw new RuntimeException("Failed to initialize OSS client", e);
        }
    }

    @PreDestroy
    public void shutdown() {
        if (ossClient != null) {
            ossClient.shutdown();
            log.info("OSS client has been shut down.");
        }
    }

    public String upload(String fileName, byte[] bytes) {
        ossClient.putObject(bucketName, fileName, new ByteArrayInputStream(bytes));
        // 修复URL格式，正确的OSS访问URL应该是 http(s)://bucketname.endpoint/objectname
        return endpoint.replace("https://", "https://" + bucketName + ".") + "/" + fileName;
    }

    public String upload(String fileName, InputStream stream) {
        ossClient.putObject(bucketName, fileName, stream);
        // 修复URL格式，正确的OSS访问URL应该是 http(s)://bucketname.endpoint/objectname
        return endpoint.replace("https://", "https://" + bucketName + ".") + "/" + fileName;
    }

    public void delete(String fileName) {
        VoidResult result = ossClient.deleteObject(bucketName, fileName);
        System.out.println(JSONObject.toJSONString(result));

    }
}
