package com.ywcx.cx_ai_slightly.utils;

import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;

/**
 * 密码工具类
 * 
 * 提供密码加密和验证功能
 */
public class PasswordUtil {

    private static final PasswordEncoder passwordEncoder = new BCryptPasswordEncoder();

    /**
     * 加密密码
     * 
     * @param rawPassword 原始密码
     * @return 加密后的密码
     */
    public static String encodePassword(String rawPassword) {
        if (rawPassword == null || rawPassword.trim().isEmpty()) {
            throw new IllegalArgumentException("密码不能为空");
        }
        return passwordEncoder.encode(rawPassword);
    }

    /**
     * 验证密码
     * 
     * @param rawPassword 原始密码
     * @param encodedPassword 加密后的密码
     * @return 是否匹配
     */
    public static boolean matches(String rawPassword, String encodedPassword) {
        if (rawPassword == null || encodedPassword == null) {
            return false;
        }
        return passwordEncoder.matches(rawPassword, encodedPassword);
    }

    /**
     * 获取PasswordEncoder实例
     * 
     * @return PasswordEncoder
     */
    public static PasswordEncoder getPasswordEncoder() {
        return passwordEncoder;
    }

    /**
     * 生成随机密码
     * 
     * @param length 密码长度
     * @return 随机密码
     */
    public static String generateRandomPassword(int length) {
        if (length < 6 || length > 32) {
            throw new IllegalArgumentException("密码长度必须在6-32位之间");
        }
        
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789@$!%*?&";
        StringBuilder password = new StringBuilder();
        
        // 确保至少包含一个大写字母、一个小写字母和一个数字
        password.append(chars.charAt((int) (Math.random() * 26))); // 大写字母
        password.append(chars.charAt(26 + (int) (Math.random() * 26))); // 小写字母
        password.append(chars.charAt(52 + (int) (Math.random() * 10))); // 数字
        
        // 填充剩余字符
        for (int i = 3; i < length; i++) {
            password.append(chars.charAt((int) (Math.random() * chars.length())));
        }
        
        return password.toString();
    }

    /**
     * 检查密码强度
     * 
     * @param password 密码
     * @return 密码强度等级：1-弱，2-中等，3-强
     */
    public static int checkPasswordStrength(String password) {
        if (password == null || password.length() < 6) {
            return 1;
        }

        int score = 0;
        
        // 长度评分
        if (password.length() >= 8) score++;
        if (password.length() >= 12) score++;
        
        // 字符类型评分
        if (password.matches(".*[a-z].*")) score++; // 小写字母
        if (password.matches(".*[A-Z].*")) score++; // 大写字母
        if (password.matches(".*\\d.*")) score++; // 数字
        if (password.matches(".*[@$!%*?&].*")) score++; // 特殊字符
        
        if (score <= 2) return 1; // 弱密码
        if (score <= 4) return 2; // 中等密码
        return 3; // 强密码
    }

    /**
     * 验证密码格式
     * 
     * @param password 密码
     * @return 是否符合基本格式要求
     */
    public static boolean isValidPassword(String password) {
        if (password == null || password.length() < 6 || password.length() > 32) {
            return false;
        }
        
        // 至少包含字母和数字
        return password.matches("^(?=.*[a-zA-Z])(?=.*\\d)[a-zA-Z\\d@$!%*?&]{6,32}$");
    }
}