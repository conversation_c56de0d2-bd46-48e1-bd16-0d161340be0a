package com.ywcx.cx_ai_slightly.utils;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ywcx.cx_ai_slightly.entity.request.TtsRequest;
import com.ywcx.cx_ai_slightly.enums.ErrorCode;
import com.ywcx.cx_ai_slightly.exception.BusinessException;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;

import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Base64;
import java.util.UUID;



/**
 * <AUTHOR>
 * @create 2025-09-01
 */
@Slf4j
@Component
public class TTSUtil {
    public static final String API_URL = "https://openspeech.bytedance.com/api/v1/tts";
    public static String appid = "4650115744";
    public static String accessToken = "LYMMOGrC99uJJUi6p6Ic7mTJZdZMy-gB";

    @Resource
    private AliOssUtil aliOssUtil;

    public String sendTts(String q) throws IOException {
        // Defensive check for null or blank input
        if (q == null || q.trim().isEmpty()) {
            log.warn("TTS input is null or empty. Skipping request.");
            return null; // Return null or an empty string to signify no audio was generated
        }

        TtsRequest ttsRequest = TtsRequest.builder()
                .app(TtsRequest.App.builder()
                        .appid(appid)
                        .cluster("volcano_tts")
                        .build())
                .user(TtsRequest.User.builder()
                        .uid("uid")
                        .build())
                .audio(TtsRequest.Audio.builder()
                        .encoding("mp3")
                        .voiceType("BV417_streaming")
                        .build())
                .request(TtsRequest.Request.builder()
                        .reqID(UUID.randomUUID().toString())
                        .operation("query")
                        .text(q)
                        .build())
                .build();

        String reqBody = JSON.toJSONString(ttsRequest);
        log.info("reqBody: {}", reqBody);

        // 2. 发送HTTP请求到火山引擎
        Headers headers = new Headers.Builder().add("Authorization", "Bearer; " + accessToken).build();
        JSONObject json;
        try {
            json = HttpClient.post(API_URL, headers, reqBody);
        } catch (IOException e) {
            log.error("HTTP request to TTS service failed", e);
            throw new BusinessException(ErrorCode.SYSTEM_ERROR, "TTS service request failed");
        }

        // Check for error code from the service
        if (json == null || json.getInteger("code") > 3000) {
            log.error("TTS service returned an error: {}", json != null ? json.toJSONString() : "null response");
            // Do not proceed, return null as the operation failed
            return null;
        }

        String data = json.getString("data");
        if (data == null) {
            log.error("TTS service response contains no data field.");
            return null;
        }

        Base64.Decoder decoder = Base64.getDecoder();
        byte[] bytes = decoder.decode(data);
        String uuid = IdUtil.simpleUUID();

        return aliOssUtil.upload("audio-service_temp" + uuid + ".mp3", bytes);
    }

    public TTSUtil() throws IOException {
    }
}
