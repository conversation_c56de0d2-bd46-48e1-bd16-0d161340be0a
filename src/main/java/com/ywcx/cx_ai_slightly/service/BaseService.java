package com.ywcx.cx_ai_slightly.service;

import com.ywcx.cx_ai_slightly.common.Result;

/**
 * 基础服务接口
 * 
 * 定义所有业务服务的通用方法和规范
 * 提供统一的服务层接口标准
 * 
 * @param <T> 实体类型
 * @param <ID> 主键类型
 */
public interface BaseService<T, ID> {

    /**
     * 根据ID查询实体
     * 
     * @param id 主键ID
     * @return Result<T> 查询结果，包含实体数据
     */
    Result<T> findById(ID id);

    /**
     * 保存实体
     * 
     * @param entity 要保存的实体
     * @return Result<T> 保存结果，包含保存后的实体数据
     */
    Result<T> save(T entity);

    /**
     * 更新实体
     * 
     * @param entity 要更新的实体
     * @return Result<T> 更新结果，包含更新后的实体数据
     */
    Result<T> update(T entity);

    /**
     * 根据ID删除实体
     * 
     * @param id 主键ID
     * @return Result<Boolean> 删除结果，true表示删除成功
     */
    Result<Boolean> deleteById(ID id);

    /**
     * 检查实体是否存在
     * 
     * @param id 主键ID
     * @return Result<Boolean> 检查结果，true表示存在
     */
    Result<Boolean> existsById(ID id);

    /**
     * 验证实体数据
     * 
     * @param entity 要验证的实体
     * @return boolean true表示验证通过，false表示验证失败
     */
    default boolean validateEntity(T entity) {
        return entity != null;
    }

    /**
     * 获取服务名称
     * 
     * @return String 服务名称
     */
    default String getServiceName() {
        return this.getClass().getSimpleName();
    }
}