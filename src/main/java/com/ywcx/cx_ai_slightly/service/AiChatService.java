package com.ywcx.cx_ai_slightly.service;

import com.ywcx.cx_ai_slightly.common.Result;
import reactor.core.publisher.Flux;

/**
 * AI聊天服务接口
 * 
 * 提供AI对话相关的业务逻辑处理
 * 包括简单对话、天气查询、内容过滤等功能
 */
public interface AiChatService {

    /**
     * 简单AI对话
     * 
     * 处理用户的简单对话请求，返回AI生成的回复
     * 
     * @param message 用户消息内容
     * @return Result<String> AI回复结果
     */
    Result<String> simpleChat(String message);

    /**
     * 天气查询对话
     * 
     * 根据城市名称生成创意性的天气描述
     * 
     * @param city 城市名称
     * @return Result<String> 天气描述结果
     */
    Result<String> weatherChat(String city);

    /**
     * 智能问答
     * 
     * 处理复杂的问答请求，支持上下文理解
     * 
     * @param question 用户问题
     * @param context 上下文信息（可选）
     * @return Result<String> 问答结果
     */
    Result<String> intelligentQA(String question, String context);

    /**
     * 内容总结
     * 
     * 对长文本内容进行智能总结
     * 
     * @param content 要总结的内容
     * @param maxLength 总结的最大长度
     * @return Result<String> 总结结果
     */
    Result<String> summarizeContent(String content, Integer maxLength);

    /**
     * 文本翻译
     * 
     * 将文本翻译成指定语言
     * 
     * @param text 要翻译的文本
     * @param targetLanguage 目标语言
     * @return Result<String> 翻译结果
     */
    Result<String> translateText(String text, String targetLanguage);

    /**
     * 代码解释
     * 
     * 解释代码的功能和逻辑
     * 
     * @param code 代码内容
     * @param language 编程语言
     * @return Result<String> 代码解释结果
     */
    Result<String> explainCode(String code, String language);

    /**
     * 创意写作
     * 
     * 根据主题生成创意内容
     * 
     * @param topic 写作主题
     * @param style 写作风格
     * @param length 内容长度要求
     * @return Result<String> 创意写作结果
     */
    Result<String> creativeWriting(String topic, String style, Integer length);

    /**
     * 验证消息内容
     * 
     * 检查消息内容是否符合要求
     * 
     * @param message 消息内容
     * @return boolean true表示验证通过
     */
    boolean validateMessage(String message);

    /**
     * 验证城市名称
     * 
     * 检查城市名称是否有效
     * 
     * @param city 城市名称
     * @return boolean true表示验证通过
     */
    boolean validateCityName(String city);

    /**
     * 内容安全检查
     * 
     * 检查内容是否包含敏感信息
     * 
     * @param content 要检查的内容
     * @return boolean true表示内容安全
     */
    boolean isContentSafe(String content);

    /**
     * 获取AI模型状态
     * 
     * 检查AI模型的可用性和状态
     * 
     * @return Result<String> 模型状态信息
     */
    Result<String> getModelStatus();

    /**
     * 清理对话历史
     * 
     * 清理指定用户的对话历史记录
     * 
     * @param userId 用户ID
     * @return Result<Boolean> 清理结果
     */
    Result<Boolean> clearChatHistory(String userId);

    /**
     * 流式AI对话
     *
     * @param message 用户消息内容
     * @return Flux<String> AI回复的响应流
     */
    Flux<String> streamingChat(String message);
}