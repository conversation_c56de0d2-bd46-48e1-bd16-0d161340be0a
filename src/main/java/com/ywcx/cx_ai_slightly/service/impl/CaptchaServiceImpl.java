package com.ywcx.cx_ai_slightly.service.impl;

import com.ywcx.cx_ai_slightly.config.AntiCrawlerConfig;
import com.ywcx.cx_ai_slightly.service.CaptchaService;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Base64;
import java.util.Random;
import java.util.concurrent.TimeUnit;

/**
 * 验证码服务实现
 */
@Slf4j
@Service
public class CaptchaServiceImpl implements CaptchaService {
    
    private static final String CAPTCHA_KEY_PREFIX = "captcha:";
    private static final String CAPTCHA_CHARS = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    private static final int CAPTCHA_LENGTH = 4;
    private static final int CAPTCHA_WIDTH = 120;
    private static final int CAPTCHA_HEIGHT = 40;
    private static final int CAPTCHA_EXPIRE_MINUTES = 5;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    @Autowired
    private AntiCrawlerConfig antiCrawlerConfig;
    
    private final Random random = new Random();
    
    @Override
    public String generateCaptcha(String sessionId) {
        // 生成随机验证码
        String captchaText = generateRandomText();
        
        // 存储到Redis
        String key = CAPTCHA_KEY_PREFIX + sessionId;
        redisTemplate.opsForValue().set(key, captchaText.toUpperCase(), CAPTCHA_EXPIRE_MINUTES, TimeUnit.MINUTES);
        
        // 生成验证码图片
        BufferedImage image = createCaptchaImage(captchaText);
        
        // 转换为Base64
        return imageToBase64(image);
    }
    
    @Override
    public boolean verifyCaptcha(String sessionId, String captcha) {
        if (captcha == null || captcha.trim().isEmpty()) {
            return false;
        }
        
        String key = CAPTCHA_KEY_PREFIX + sessionId;
        String storedCaptcha = (String) redisTemplate.opsForValue().get(key);
        
        if (storedCaptcha == null) {
            return false;
        }
        
        boolean isValid = storedCaptcha.equalsIgnoreCase(captcha.trim());
        
        if (isValid) {
            // 验证成功后删除验证码
            redisTemplate.delete(key);
        }
        
        return isValid;
    }
    
    @Override
    public boolean requiresCaptcha(HttpServletRequest request) {
        if (!antiCrawlerConfig.isEnabled()) {
            return false;
        }
        
        String requestPath = request.getRequestURI();
        return antiCrawlerConfig.getCaptchaRequiredPaths().stream()
                .anyMatch(requestPath::startsWith);
    }
    
    @Override
    public void clearCaptcha(String sessionId) {
        String key = CAPTCHA_KEY_PREFIX + sessionId;
        redisTemplate.delete(key);
    }
    
    /**
     * 生成随机验证码文本
     */
    private String generateRandomText() {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < CAPTCHA_LENGTH; i++) {
            sb.append(CAPTCHA_CHARS.charAt(random.nextInt(CAPTCHA_CHARS.length())));
        }
        return sb.toString();
    }
    
    /**
     * 创建验证码图片
     */
    private BufferedImage createCaptchaImage(String captchaText) {
        BufferedImage image = new BufferedImage(CAPTCHA_WIDTH, CAPTCHA_HEIGHT, BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = image.createGraphics();
        
        // 设置抗锯齿
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        
        // 填充背景
        g2d.setColor(Color.WHITE);
        g2d.fillRect(0, 0, CAPTCHA_WIDTH, CAPTCHA_HEIGHT);
        
        // 绘制干扰线
        drawNoiseLine(g2d);
        
        // 绘制验证码文字
        drawCaptchaText(g2d, captchaText);
        
        // 添加噪点
        drawNoisePoints(g2d);
        
        g2d.dispose();
        return image;
    }
    
    /**
     * 绘制干扰线
     */
    private void drawNoiseLine(Graphics2D g2d) {
        g2d.setColor(new Color(random.nextInt(256), random.nextInt(256), random.nextInt(256)));
        for (int i = 0; i < 5; i++) {
            int x1 = random.nextInt(CAPTCHA_WIDTH);
            int y1 = random.nextInt(CAPTCHA_HEIGHT);
            int x2 = random.nextInt(CAPTCHA_WIDTH);
            int y2 = random.nextInt(CAPTCHA_HEIGHT);
            g2d.drawLine(x1, y1, x2, y2);
        }
    }
    
    /**
     * 绘制验证码文字
     */
    private void drawCaptchaText(Graphics2D g2d, String captchaText) {
        Font font = new Font("Arial", Font.BOLD, 24);
        g2d.setFont(font);
        
        FontMetrics fm = g2d.getFontMetrics();
        int x = (CAPTCHA_WIDTH - fm.stringWidth(captchaText)) / 2;
        int y = (CAPTCHA_HEIGHT + fm.getAscent()) / 2;
        
        // 为每个字符设置不同的颜色和角度
        for (int i = 0; i < captchaText.length(); i++) {
            char c = captchaText.charAt(i);
            g2d.setColor(new Color(random.nextInt(200), random.nextInt(200), random.nextInt(200)));
            
            // 计算字符位置
            int charX = x + i * (fm.stringWidth(String.valueOf(c)) + 5);
            int charY = y + random.nextInt(6) - 3; // 随机上下偏移
            
            // 旋转角度
            double angle = (random.nextDouble() - 0.5) * 0.5; // -0.25 到 0.25 弧度
            g2d.rotate(angle, charX, charY);
            g2d.drawString(String.valueOf(c), charX, charY);
            g2d.rotate(-angle, charX, charY); // 恢复旋转
        }
    }
    
    /**
     * 绘制噪点
     */
    private void drawNoisePoints(Graphics2D g2d) {
        for (int i = 0; i < 50; i++) {
            int x = random.nextInt(CAPTCHA_WIDTH);
            int y = random.nextInt(CAPTCHA_HEIGHT);
            g2d.setColor(new Color(random.nextInt(256), random.nextInt(256), random.nextInt(256)));
            g2d.fillOval(x, y, 1, 1);
        }
    }
    
    /**
     * 将图片转换为Base64编码
     */
    private String imageToBase64(BufferedImage image) {
        try {
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ImageIO.write(image, "png", baos);
            byte[] imageBytes = baos.toByteArray();
            return "data:image/png;base64," + Base64.getEncoder().encodeToString(imageBytes);
        } catch (IOException e) {
            log.error("验证码图片转换Base64失败", e);
            throw new RuntimeException("验证码生成失败", e);
        }
    }
}