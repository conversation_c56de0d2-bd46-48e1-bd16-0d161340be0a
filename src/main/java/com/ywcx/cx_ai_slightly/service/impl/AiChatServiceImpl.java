package com.ywcx.cx_ai_slightly.service.impl;

import com.ywcx.cx_ai_slightly.common.Result;
import com.ywcx.cx_ai_slightly.enums.ErrorCode;
import com.ywcx.cx_ai_slightly.exception.BusinessException;
import com.ywcx.cx_ai_slightly.service.AiChatService;
import com.ywcx.cx_ai_slightly.utils.RedisUtil;
import dev.langchain4j.data.message.AiMessage;
import dev.langchain4j.model.StreamingResponseHandler;
import dev.langchain4j.model.chat.ChatLanguageModel;
import dev.langchain4j.model.chat.StreamingChatLanguageModel;
import dev.langchain4j.model.input.Prompt;
import dev.langchain4j.model.input.PromptTemplate;
import dev.langchain4j.model.output.Response;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import reactor.core.publisher.Flux;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;

/**
 * AI聊天服务实现类
 * 
 * 实现AI对话相关的业务逻辑处理
 * 包括简单对话、天气查询、内容过滤等功能
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class AiChatServiceImpl implements AiChatService {

    private final ChatLanguageModel chatLanguageModel;
    @Autowired
    StreamingChatLanguageModel streamingChatLanguageModel;
    private final RedisUtil redisUtil;

    // 消息长度限制
    private static final int MAX_MESSAGE_LENGTH = 2000;
    private static final int MAX_CITY_NAME_LENGTH = 50;
    private static final int MAX_CONTENT_LENGTH = 10000;
    private static final int DEFAULT_SUMMARY_LENGTH = 200;

    // 城市名称正则表达式（支持中文、英文、空格、连字符）
    private static final Pattern CITY_NAME_PATTERN = Pattern.compile("^[\\u4e00-\\u9fa5a-zA-Z\\s\\-]{1,50}$");
    
    // 敏感词列表（实际项目中应该从配置文件或数据库加载）
    private static final String[] SENSITIVE_WORDS = {"暴力", "色情", "政治敏感", "违法"};

    // Redis键前缀
    private static final String CHAT_HISTORY_PREFIX = "chat:history:";
    private static final String AI_CACHE_PREFIX = "ai:cache:";

    /**
     * 简单AI对话
     * 
     * 处理用户的简单对话请求，返回AI生成的回复
     * 
     * @param message 用户消息内容
     * @return Result<String> AI回复结果
     */
    @Override
    public Result<String> simpleChat(String message) {
        log.info("处理简单AI对话请求，消息长度: {}", message != null ? message.length() : 0);
        
        try {
            // 参数验证
            if (!StringUtils.hasText(message)) {
                throw new BusinessException(ErrorCode.PARAM_ERROR, "消息内容不能为空");
            }

            // 验证消息内容
            if (!validateMessage(message)) {
                throw new BusinessException(ErrorCode.MESSAGE_TOO_LONG, 
                    String.format("消息长度不能超过%d个字符", MAX_MESSAGE_LENGTH));
            }

            // 内容安全检查
            if (!isContentSafe(message)) {
                throw new BusinessException(ErrorCode.AI_CONTENT_FILTERED, "消息内容包含敏感信息");
            }

            // 检查缓存
            String cachedResponse = getCachedResponse(message);
            if (cachedResponse != null) {
                log.debug("返回缓存的AI回复");
                return Result.success(cachedResponse);
            }

            // 调用AI模型
            String response = callAiModel(message);
            
            // 缓存响应结果
            cacheResponse(message, response);

            // 记录对话历史（可选）
            recordChatHistory("anonymous", message, response);

            log.info("简单AI对话处理完成，回复长度: {}", response.length());
            return Result.success(response);

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("简单AI对话处理异常: ", e);
            throw new BusinessException(ErrorCode.AI_SERVICE_ERROR, "AI服务调用失败: " + e.getMessage());
        }
    }

    /**
     * 天气查询对话
     * 
     * 根据城市名称生成创意性的天气描述
     * 
     * @param city 城市名称
     * @return Result<String> 天气描述结果
     */
    @Override
    public Result<String> weatherChat(String city) {
        log.info("处理天气查询请求，城市: {}", city);
        
        try {
            // 参数验证
            if (!StringUtils.hasText(city)) {
                city = "北京";
            }

            // 验证城市名称
            if (!validateCityName(city)) {
                throw new BusinessException(ErrorCode.CITY_NAME_INVALID, "城市名称格式无效");
            }

            // 检查缓存
            String cacheKey = "weather:" + city;
            String cachedResponse = getCachedResponse(cacheKey);
            if (cachedResponse != null) {
                log.debug("返回缓存的天气查询结果");
                return Result.success(cachedResponse);
            }

            // 构建天气查询提示词
            PromptTemplate promptTemplate = PromptTemplate.from(
                "请用创意和诗意的方式描述{{city}}今天的天气情况。" +
                "要求：1. 语言优美生动 2. 包含温度、湿度、风力等信息 3. 字数控制在150字以内 4. 避免过于夸张"
            );
            
            Prompt prompt = promptTemplate.apply(Map.of("city", city));
            
            // 调用AI模型
            String response = callAiModel(prompt.text());
            
            // 缓存响应结果（天气信息缓存时间较短）
            cacheResponse(cacheKey, response, 30, TimeUnit.MINUTES);

            log.info("天气查询处理完成，城市: {}, 回复长度: {}", city, response.length());
            return Result.success(response);

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("天气查询处理异常: ", e);
            throw new BusinessException(ErrorCode.AI_SERVICE_ERROR, "天气查询服务调用失败: " + e.getMessage());
        }
    }

    /**
     * 智能问答
     * 
     * 处理复杂的问答请求，支持上下文理解
     * 
     * @param question 用户问题
     * @param context 上下文信息（可选）
     * @return Result<String> 问答结果
     */
    @Override
    public Result<String> intelligentQA(String question, String context) {
        log.info("处理智能问答请求，问题长度: {}, 上下文长度: {}", 
                question != null ? question.length() : 0, 
                context != null ? context.length() : 0);
        
        try {
            // 参数验证
            if (!StringUtils.hasText(question)) {
                throw new BusinessException(ErrorCode.PARAM_ERROR, "问题内容不能为空");
            }

            if (!validateMessage(question)) {
                throw new BusinessException(ErrorCode.MESSAGE_TOO_LONG, "问题内容过长");
            }

            // 内容安全检查
            if (!isContentSafe(question)) {
                throw new BusinessException(ErrorCode.AI_CONTENT_FILTERED, "问题内容包含敏感信息");
            }

            // 构建问答提示词
            StringBuilder promptBuilder = new StringBuilder();
            promptBuilder.append("请回答以下问题：").append(question);
            
            if (StringUtils.hasText(context)) {
                promptBuilder.append("\n\n参考上下文：").append(context);
            }
            
            promptBuilder.append("\n\n要求：1. 回答准确专业 2. 逻辑清晰 3. 语言简洁 4. 如果不确定请说明");

            // 调用AI模型
            String response = callAiModel(promptBuilder.toString());

            log.info("智能问答处理完成，回复长度: {}", response.length());
            return Result.success(response);

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("智能问答处理异常: ", e);
            throw new BusinessException(ErrorCode.AI_SERVICE_ERROR, "智能问答服务调用失败: " + e.getMessage());
        }
    }

    /**
     * 内容总结
     * 
     * 对长文本内容进行智能总结
     * 
     * @param content 要总结的内容
     * @param maxLength 总结的最大长度
     * @return Result<String> 总结结果
     */
    @Override
    public Result<String> summarizeContent(String content, Integer maxLength) {
        log.info("处理内容总结请求，内容长度: {}, 目标长度: {}", 
                content != null ? content.length() : 0, maxLength);
        
        try {
            // 参数验证
            if (!StringUtils.hasText(content)) {
                throw new BusinessException(ErrorCode.PARAM_ERROR, "要总结的内容不能为空");
            }

            if (content.length() > MAX_CONTENT_LENGTH) {
                throw new BusinessException(ErrorCode.PARAM_ERROR, 
                    String.format("内容长度不能超过%d个字符", MAX_CONTENT_LENGTH));
            }

            if (maxLength == null || maxLength <= 0) {
                maxLength = DEFAULT_SUMMARY_LENGTH;
            }

            // 内容安全检查
            if (!isContentSafe(content)) {
                throw new BusinessException(ErrorCode.AI_CONTENT_FILTERED, "内容包含敏感信息");
            }

            // 构建总结提示词
            String prompt = String.format(
                "请对以下内容进行总结，要求：\n" +
                "1. 总结长度控制在%d字以内\n" +
                "2. 保留核心要点和关键信息\n" +
                "3. 语言简洁明了\n" +
                "4. 逻辑结构清晰\n\n" +
                "内容：\n%s", 
                maxLength, content
            );

            // 调用AI模型
            String response = callAiModel(prompt);

            log.info("内容总结处理完成，原文长度: {}, 总结长度: {}", content.length(), response.length());
            return Result.success(response);

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("内容总结处理异常: ", e);
            throw new BusinessException(ErrorCode.AI_SERVICE_ERROR, "内容总结服务调用失败: " + e.getMessage());
        }
    }

    /**
     * 文本翻译
     * 
     * 将文本翻译成指定语言
     * 
     * @param text 要翻译的文本
     * @param targetLanguage 目标语言
     * @return Result<String> 翻译结果
     */
    @Override
    public Result<String> translateText(String text, String targetLanguage) {
        log.info("处理文本翻译请求，文本长度: {}, 目标语言: {}", 
                text != null ? text.length() : 0, targetLanguage);
        
        try {
            // 参数验证
            if (!StringUtils.hasText(text)) {
                throw new BusinessException(ErrorCode.PARAM_ERROR, "要翻译的文本不能为空");
            }

            if (!StringUtils.hasText(targetLanguage)) {
                targetLanguage = "中文";
            }

            if (!validateMessage(text)) {
                throw new BusinessException(ErrorCode.MESSAGE_TOO_LONG, "文本内容过长");
            }

            // 构建翻译提示词
            String prompt = String.format(
                "请将以下文本翻译成%s：\n\n%s\n\n" +
                "要求：1. 翻译准确流畅 2. 保持原文语气和风格 3. 如有专业术语请保持准确性",
                targetLanguage, text
            );

            // 调用AI模型
            String response = callAiModel(prompt);

            log.info("文本翻译处理完成，原文长度: {}, 译文长度: {}", text.length(), response.length());
            return Result.success(response);

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("文本翻译处理异常: ", e);
            throw new BusinessException(ErrorCode.AI_SERVICE_ERROR, "文本翻译服务调用失败: " + e.getMessage());
        }
    }

    /**
     * 代码解释
     * 
     * 解释代码的功能和逻辑
     * 
     * @param code 代码内容
     * @param language 编程语言
     * @return Result<String> 代码解释结果
     */
    @Override
    public Result<String> explainCode(String code, String language) {
        log.info("处理代码解释请求，代码长度: {}, 编程语言: {}", 
                code != null ? code.length() : 0, language);
        
        try {
            // 参数验证
            if (!StringUtils.hasText(code)) {
                throw new BusinessException(ErrorCode.PARAM_ERROR, "代码内容不能为空");
            }

            if (!StringUtils.hasText(language)) {
                language = "未知";
            }

            if (code.length() > MAX_CONTENT_LENGTH) {
                throw new BusinessException(ErrorCode.PARAM_ERROR, "代码内容过长");
            }

            // 构建代码解释提示词
            String prompt = String.format(
                "请解释以下%s代码的功能和逻辑：\n\n```%s\n%s\n```\n\n" +
                "要求：\n" +
                "1. 详细解释代码的主要功能\n" +
                "2. 分析关键逻辑和算法\n" +
                "3. 指出可能的优化点\n" +
                "4. 使用通俗易懂的语言",
                language, language.toLowerCase(), code
            );

            // 调用AI模型
            String response = callAiModel(prompt);

            log.info("代码解释处理完成，代码长度: {}, 解释长度: {}", code.length(), response.length());
            return Result.success(response);

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("代码解释处理异常: ", e);
            throw new BusinessException(ErrorCode.AI_SERVICE_ERROR, "代码解释服务调用失败: " + e.getMessage());
        }
    }

    /**
     * 创意写作
     * 
     * 根据主题生成创意内容
     * 
     * @param topic 写作主题
     * @param style 写作风格
     * @param length 内容长度要求
     * @return Result<String> 创意写作结果
     */
    @Override
    public Result<String> creativeWriting(String topic, String style, Integer length) {
        log.info("处理创意写作请求，主题: {}, 风格: {}, 长度: {}", topic, style, length);
        
        try {
            // 参数验证
            if (!StringUtils.hasText(topic)) {
                throw new BusinessException(ErrorCode.PARAM_ERROR, "写作主题不能为空");
            }

            if (!StringUtils.hasText(style)) {
                style = "自然流畅";
            }

            if (length == null || length <= 0) {
                length = 500;
            }

            // 内容安全检查
            if (!isContentSafe(topic)) {
                throw new BusinessException(ErrorCode.AI_CONTENT_FILTERED, "主题内容包含敏感信息");
            }

            // 构建创意写作提示词
            String prompt = String.format(
                "请以'%s'为主题进行创意写作，要求：\n" +
                "1. 写作风格：%s\n" +
                "2. 内容长度：约%d字\n" +
                "3. 内容原创且富有创意\n" +
                "4. 语言生动有趣\n" +
                "5. 结构完整，逻辑清晰",
                topic, style, length
            );

            // 调用AI模型
            String response = callAiModel(prompt);

            log.info("创意写作处理完成，主题: {}, 内容长度: {}", topic, response.length());
            return Result.success(response);

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("创意写作处理异常: ", e);
            throw new BusinessException(ErrorCode.AI_SERVICE_ERROR, "创意写作服务调用失败: " + e.getMessage());
        }
    }

    /**
     * 获取AI模型状态
     * 
     * 检查AI模型的可用性和状态
     * 
     * @return Result<String> 模型状态信息
     */
    @Override
    public Result<String> getModelStatus() {
        log.info("检查AI模型状态");
        
        try {
            // 发送健康检查消息
            String healthCheckMessage = "Hello";
            String response = callAiModel(healthCheckMessage);
            
            if (StringUtils.hasText(response)) {
                String statusInfo = String.format("AI模型状态正常，响应时间: %dms", System.currentTimeMillis() % 1000);
                log.info("AI模型状态检查完成: {}", statusInfo);
                return Result.success(statusInfo);
            } else {
                throw new BusinessException(ErrorCode.AI_SERVICE_ERROR, "AI模型响应为空");
            }

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("AI模型状态检查异常: ", e);
            throw new BusinessException(ErrorCode.AI_SERVICE_ERROR, "AI模型状态检查失败: " + e.getMessage());
        }
    }

    /**
     * 清理对话历史
     * 
     * 清理指定用户的对话历史记录
     * 
     * @param userId 用户ID
     * @return Result<Boolean> 清理结果
     */
    @Override
    public Result<Boolean> clearChatHistory(String userId) {
        log.info("清理用户对话历史，用户ID: {}", userId);
        
        try {
            if (!StringUtils.hasText(userId)) {
                throw new BusinessException(ErrorCode.PARAM_ERROR, "用户ID不能为空");
            }

            String historyKey = CHAT_HISTORY_PREFIX + userId;
            redisUtil.del(historyKey);
            Boolean deleted = true;
            
            log.info("用户 {} 的对话历史清理完成，结果: {}", userId, deleted);
            return Result.success(deleted != null ? deleted : false);

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("清理对话历史异常: ", e);
            throw new BusinessException(ErrorCode.SYSTEM_ERROR, "清理对话历史失败: " + e.getMessage());
        }
    }

    // =============================验证方法=============================

    /**
     * 验证消息内容
     */
    @Override
    public boolean validateMessage(String message) {
        return StringUtils.hasText(message) && message.length() <= MAX_MESSAGE_LENGTH;
    }

    /**
     * 验证城市名称
     */
    @Override
    public boolean validateCityName(String city) {
        return StringUtils.hasText(city) && 
               city.length() <= MAX_CITY_NAME_LENGTH && 
               CITY_NAME_PATTERN.matcher(city).matches();
    }

    /**
     * 内容安全检查
     */
    @Override
    public boolean isContentSafe(String content) {
        if (!StringUtils.hasText(content)) {
            return true;
        }

        String lowerContent = content.toLowerCase();
        for (String sensitiveWord : SENSITIVE_WORDS) {
            if (lowerContent.contains(sensitiveWord.toLowerCase())) {
                log.warn("检测到敏感词: {}", sensitiveWord);
                return false;
            }
        }
        return true;
    }

    // =============================私有辅助方法=============================

    /**
     * 调用AI模型
     */
    private String callAiModel(String message) {
        try {
            log.debug("调用AI模型，消息长度: {}", message.length());
            String response = chatLanguageModel.generate(message);
            log.debug("AI模型响应长度: {}", response != null ? response.length() : 0);
            return response;
        } catch (Exception e) {
            log.error("AI模型调用失败: ", e);
            throw new BusinessException(ErrorCode.AI_SERVICE_ERROR, "AI模型调用失败: " + e.getMessage());
        }
    }

    /**
     * 获取缓存的响应
     */
    private String getCachedResponse(String key) {
        try {
            String cacheKey = AI_CACHE_PREFIX + key.hashCode();
            Object cached = redisUtil.get(cacheKey);
            return cached instanceof String ? (String) cached : null;
        } catch (Exception e) {
            log.warn("获取缓存响应失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 缓存响应结果
     */
    private void cacheResponse(String key, String response) {
        cacheResponse(key, response, 1, TimeUnit.HOURS);
    }

    /**
     * 缓存响应结果（指定过期时间）
     */
    private void cacheResponse(String key, String response, long timeout, TimeUnit unit) {
        try {
            String cacheKey = AI_CACHE_PREFIX + key.hashCode();
            redisUtil.set(cacheKey, response, timeout, unit);
        } catch (Exception e) {
            log.warn("缓存响应失败: {}", e.getMessage());
        }
    }

    /**
     * 记录对话历史
     */
    private void recordChatHistory(String userId, String question, String answer) {
        try {
            String historyKey = CHAT_HISTORY_PREFIX + userId;
            Map<String, Object> chatRecord = new HashMap<>();
            chatRecord.put("question", question);
            chatRecord.put("answer", answer);
            chatRecord.put("timestamp", System.currentTimeMillis());
            
            // 这里可以实现更复杂的历史记录逻辑，比如保存到列表中
            redisUtil.set(historyKey + ":" + System.currentTimeMillis(), chatRecord, 7, TimeUnit.DAYS);
        } catch (Exception e) {
            log.warn("记录对话历史失败: {}", e.getMessage());
        }
    }

    /**
     * 流式AI对话
     *
     * @param message 用户消息内容
     * @return Flux<String> AI回复的响应流
     */
    @Override
    public Flux<String> streamingChat(String message) {
        log.info("处理流式AI对话请求，消息长度: {}", message != null ? message.length() : 0);

        // 参数验证
        if (!StringUtils.hasText(message)) {
            return Flux.error(new BusinessException(ErrorCode.PARAM_ERROR, "消息内容不能为空"));
        }
        if (!validateMessage(message)) {
            return Flux.error(new BusinessException(ErrorCode.MESSAGE_TOO_LONG,
                    String.format("消息长度不能超过%d个字符", MAX_MESSAGE_LENGTH)));
        }
        if (!isContentSafe(message)) {
            return Flux.error(new BusinessException(ErrorCode.AI_CONTENT_FILTERED, "消息内容包含敏感信息"));
        }

        if ()

        return Flux.create(fluxSink -> {
            try {
                streamingChatLanguageModel.generate(message, new StreamingResponseHandler<AiMessage>() {
                    @Override
                    public void onNext(String token) {
                        log.debug("流式响应 token: {}", token);
                        fluxSink.next(token);
                    }

                    @Override
                    public void onComplete(Response<AiMessage> response) {
                        log.info("流式响应完成");
                        fluxSink.complete();
                    }

                    @Override
                    public void onError(Throwable error) {
                        log.error("流式响应异常: ", error);
                        fluxSink.error(new BusinessException(ErrorCode.AI_SERVICE_ERROR, "AI服务调用失败: " + error.getMessage()));
                    }
                });
            } catch (Exception e) {
                log.error("流式AI对话处理异常: ", e);
                fluxSink.error(new BusinessException(ErrorCode.AI_SERVICE_ERROR, "AI服务调用失败: " + e.getMessage()));
            }
        });
    }
}
