package com.ywcx.cx_ai_slightly.service.impl;

import com.ywcx.cx_ai_slightly.config.SmsAliConfig;
import com.ywcx.cx_ai_slightly.service.AliSmsService;
import com.ywcx.cx_ai_slightly.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Random;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @create 2025-09-03
 */
@Slf4j
@Service
public class AliSmsServiceImpl implements AliSmsService {

    @Autowired
    private RedisUtil redisUtil;

    @Override
    public void sendSms(String phone, String code) {
        try {
            // 发送短信
            SmsAliConfig.sendSmsMessage(phone, code);
            
            // 发送成功后保存到Redis，设置1分钟过期
            String key = "sms:code:" + phone;
            redisUtil.set(key, code, 1, TimeUnit.MINUTES);
            
            log.info("短信验证码发送成功，手机号: {}, 验证码: {}", phone, code);
        } catch (Exception e) {
            log.error("短信验证码发送失败，手机号: {}", phone, e);
            throw new RuntimeException("短信发送失败", e);
        }
    }

    /**
     * 生成6位随机数字验证码并发送短信
     *
     * @param phone 手机号
     * @return 验证码
     */
    public String generateAndSendSmsCode(String phone) {
        // 生成6位随机数字验证码
        String code = generateSmsCode(6);
        sendSms(phone, code);
        return code;
    }

    /**
     * 生成指定长度的数字验证码
     *
     * @param length 验证码长度
     * @return 验证码
     */
    private String generateSmsCode(int length) {
        StringBuilder sb = new StringBuilder();
        Random random = new Random();
        for (int i = 0; i < length; i++) {
            sb.append(random.nextInt(10));
        }
        return sb.toString();
    }
}