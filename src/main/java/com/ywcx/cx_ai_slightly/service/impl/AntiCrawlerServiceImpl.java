package com.ywcx.cx_ai_slightly.service.impl;

import com.ywcx.cx_ai_slightly.config.AntiCrawlerConfig;
import com.ywcx.cx_ai_slightly.service.AntiCrawlerService;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.concurrent.TimeUnit;

/**
 * 反爬虫服务实现
 */
@Slf4j
@Service
public class AntiCrawlerServiceImpl implements AntiCrawlerService {
    
    private static final String RATE_LIMIT_KEY_PREFIX = "anti_crawler:rate_limit:";
    private static final String BAN_KEY_PREFIX = "anti_crawler:ban:";
    
    @Autowired
    private AntiCrawlerConfig antiCrawlerConfig;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    @Override
    public boolean isRequestAllowed(HttpServletRequest request) {
        if (!antiCrawlerConfig.isEnabled()) {
            return true;
        }
        
        String clientIp = getClientIp(request);
        String userAgent = request.getHeader("User-Agent");
        
        // 检查白名单
        if (antiCrawlerConfig.getWhitelistIps().contains(clientIp)) {
            return true;
        }
        
        // 检查IP黑名单
        if (antiCrawlerConfig.getBlacklistIps().contains(clientIp)) {
            log.warn("IP {} 在黑名单中，拒绝访问", clientIp);
            return false;
        }
        
        // 检查IP是否被封禁
        if (isIpBanned(clientIp)) {
            log.warn("IP {} 被临时封禁，拒绝访问", clientIp);
            return false;
        }
        
        // 检查User-Agent黑名单
        if (userAgent != null && isUserAgentBlacklisted(userAgent)) {
            log.warn("User-Agent {} 在黑名单中，拒绝访问", userAgent);
            banIp(clientIp, antiCrawlerConfig.getRateLimit().getBanDurationSeconds());
            return false;
        }
        
        // 检查请求频率
        if (!checkRateLimit(clientIp)) {
            log.warn("IP {} 请求频率过高，临时封禁", clientIp);
            banIp(clientIp, antiCrawlerConfig.getRateLimit().getBanDurationSeconds());
            return false;
        }
        
        // 检查请求头
        if (!validateRequestHeaders(request)) {
            log.warn("IP {} 请求头异常，拒绝访问", clientIp);
            return false;
        }
        
        return true;
    }
    
    @Override
    public void recordRequest(HttpServletRequest request) {
        String clientIp = getClientIp(request);
        String key = RATE_LIMIT_KEY_PREFIX + clientIp;
        
        // 增加请求计数
        Long count = redisTemplate.opsForValue().increment(key);
        if (count == 1) {
            // 设置过期时间
            redisTemplate.expire(key, antiCrawlerConfig.getRateLimit().getWindowSeconds(), TimeUnit.SECONDS);
        }
    }
    
    @Override
    public boolean isIpBanned(String ip) {
        String key = BAN_KEY_PREFIX + ip;
        return Boolean.TRUE.equals(redisTemplate.hasKey(key));
    }
    
    @Override
    public void banIp(String ip, int durationSeconds) {
        String key = BAN_KEY_PREFIX + ip;
        redisTemplate.opsForValue().set(key, System.currentTimeMillis(), durationSeconds, TimeUnit.SECONDS);
        log.info("IP {} 被封禁 {} 秒", ip, durationSeconds);
    }
    
    @Override
    public void unbanIp(String ip) {
        String key = BAN_KEY_PREFIX + ip;
        redisTemplate.delete(key);
        log.info("IP {} 已解封", ip);
    }
    
    @Override
    public String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (StringUtils.hasText(ip) && !"unknown".equalsIgnoreCase(ip)) {
            // 多次反向代理后会有多个IP值，第一个为真实IP
            int index = ip.indexOf(',');
            if (index != -1) {
                return ip.substring(0, index);
            } else {
                return ip;
            }
        }
        
        ip = request.getHeader("X-Real-IP");
        if (StringUtils.hasText(ip) && !"unknown".equalsIgnoreCase(ip)) {
            return ip;
        }
        
        ip = request.getHeader("Proxy-Client-IP");
        if (StringUtils.hasText(ip) && !"unknown".equalsIgnoreCase(ip)) {
            return ip;
        }
        
        ip = request.getHeader("WL-Proxy-Client-IP");
        if (StringUtils.hasText(ip) && !"unknown".equalsIgnoreCase(ip)) {
            return ip;
        }
        
        ip = request.getHeader("HTTP_CLIENT_IP");
        if (StringUtils.hasText(ip) && !"unknown".equalsIgnoreCase(ip)) {
            return ip;
        }
        
        ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        if (StringUtils.hasText(ip) && !"unknown".equalsIgnoreCase(ip)) {
            return ip;
        }
        
        return request.getRemoteAddr();
    }
    
    /**
     * 检查User-Agent是否在黑名单中
     */
    private boolean isUserAgentBlacklisted(String userAgent) {
        String lowerUserAgent = userAgent.toLowerCase();
        return antiCrawlerConfig.getBlacklistUserAgents().stream()
                .anyMatch(blacklistAgent -> lowerUserAgent.contains(blacklistAgent.toLowerCase()));
    }
    
    /**
     * 检查请求频率限制
     */
    private boolean checkRateLimit(String ip) {
        String key = RATE_LIMIT_KEY_PREFIX + ip;
        Object value = redisTemplate.opsForValue().get(key);
        
        if (value == null) {
            return true;
        }
        
        // 安全地处理可能的Integer或Long类型
        Long count;
        if (value instanceof Number) {
            count = ((Number) value).longValue();
        } else {
            count = Long.valueOf(value.toString());
        }
        
        return count <= antiCrawlerConfig.getRateLimit().getMaxRequests();
    }
    
    /**
     * 验证请求头
     */
    private boolean validateRequestHeaders(HttpServletRequest request) {
        // 检查必要的请求头
        String userAgent = request.getHeader("User-Agent");
        if (!StringUtils.hasText(userAgent)) {
            return false;
        }
        
        // 检查Accept头
        String accept = request.getHeader("Accept");
        if (!StringUtils.hasText(accept)) {
            return false;
        }
        
        // 检查可疑的请求头组合
        String connection = request.getHeader("Connection");
        if ("close".equalsIgnoreCase(connection) && userAgent.contains("python")) {
            return false;
        }
        
        return true;
    }
}