package com.ywcx.cx_ai_slightly.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.IdUtil;
import com.ywcx.cx_ai_slightly.entity.User;
import com.ywcx.cx_ai_slightly.enums.ErrorCode;
import com.ywcx.cx_ai_slightly.exception.BusinessException;
import com.ywcx.cx_ai_slightly.repository.UserRepository;
import com.ywcx.cx_ai_slightly.service.UserService;
import com.ywcx.cx_ai_slightly.utils.PasswordUtil;
import com.ywcx.cx_ai_slightly.utils.RedisUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;

/**
 * 用户服务实现类
 * 实现用户相关的业务逻辑处理
 * 包括用户认证、注册、信息管理等功能
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class UserServiceImpl implements UserService {

    private final RedisUtil redisUtil;
    private final UserRepository userRepository;

    // 用户名正则表达式：4-20位字母、数字、下划线
    private static final Pattern USERNAME_PATTERN = Pattern.compile("^[a-zA-Z0-9_]{4,20}$");
    
    // 密码正则表达式：6-20位，至少包含字母和数字
    private static final Pattern PASSWORD_PATTERN = Pattern.compile("^(?=.*[a-zA-Z])(?=.*\\d)[a-zA-Z\\d@$!%*?&]{6,20}$");
    
    // 邮箱正则表达式
    private static final Pattern EMAIL_PATTERN = Pattern.compile("^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$");

    // Redis键前缀
    private static final String USER_INFO_PREFIX = "user:info:";
    private static final String USER_LOGIN_PREFIX = "user:login:";

    /**
     * 用户登录
     * 
     * 验证用户凭据并生成访问令牌
     * 
     * @param phoneNumber 手机号
     * @param password 密码
     */
    @Override
    public Map<String, Object> login(String phoneNumber, String password) {
        log.info("用户登录请求，手机号: {}", phoneNumber);
        
        try {
            // 参数验证
            if (!StringUtils.hasText(phoneNumber) || !StringUtils.hasText(password)) {
                throw new BusinessException(ErrorCode.PARAM_ERROR, "手机号和密码不能为空");
            }

            // 用户认证
            User user = authenticateAndGetUser(phoneNumber, password);
            if (user == null) {
                log.warn("用户登录失败，手机号或密码错误: {}", phoneNumber);
                throw new BusinessException(ErrorCode.UNAUTHORIZED, "用户名或密码错误");
            }

            // 执行Sa-Token登录
            StpUtil.login(phoneNumber);

            // 更新用户登录信息
            updateUserLoginInfo(user.getId(), "127.0.0.1"); // TODO: 获取真实IP地址

            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("token", StpUtil.getTokenValue());
            result.put("username", user.getUserType());
            result.put("loginId", StpUtil.getLoginId());
            result.put("tokenTimeout", StpUtil.getTokenTimeout());
            result.put("loginTime", System.currentTimeMillis());
            result.put("userId", user.getId());
            result.put("nickname", user.getNickname());

            // 缓存用户登录信息
            cacheUserLoginInfo(phoneNumber, result);

            log.info("用户 {} 登录成功，token: {}", phoneNumber, StpUtil.getTokenValue());
            return result;

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("用户登录异常: ", e);
            throw new BusinessException(ErrorCode.SYSTEM_ERROR, "登录失败: " + e.getMessage());
        }
    }

    /**
     * 用户注册
     * 
     * 创建新用户账户
     *
     * @param password 密码
     * @param phoneNumber 手机号
     */
    @Override
    public void register(String password, String phoneNumber) {
        log.info("用户注册请求，手机号: {}", phoneNumber);
        
        try {
            // 参数验证
            if (!StringUtils.hasText(phoneNumber) || !StringUtils.hasText(password)) {
                throw new BusinessException(ErrorCode.PARAM_ERROR, "手机号和密码不能为空");
            }


            // 验证密码强度
            if (!validatePassword(password)) {
                throw new BusinessException(ErrorCode.PARAM_ERROR, "密码格式不正确，应为6-20位且包含字母和数字");
            }


            // 检查用户名是否已存在
            if (isPhoneExists(phoneNumber)) {
                throw new BusinessException(ErrorCode.PARAM_ERROR, "手机号已存在");
            }

            String username = "小白_" + IdUtil.simpleUUID();
            // 创建用户
            createUser(username, password, phoneNumber);


            log.info("用户 {} 注册成功", username);
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("用户注册异常: ", e);
            throw new BusinessException(ErrorCode.SYSTEM_ERROR, "注册失败: " + e.getMessage());
        }
    }

    /**
     * 短信验证码登录/注册
     *
     * 根据短信验证码校验手机号，用户不存在则自动注册，随后完成登录并返回token等信息
     *
     * @param phoneNumber 手机号
     * @param smsCode 短信验证码
     * @return 登录结果数据
     */
    @Override
    public Map<String, Object> loginOrRegisterBySms(String phoneNumber, String smsCode) {
        log.info("短信验证码登录/注册请求，手机号: {}", phoneNumber);

        try {
            if (!StringUtils.hasText(phoneNumber) || !StringUtils.hasText(smsCode)) {
                throw new BusinessException(ErrorCode.PARAM_ERROR, "手机号和验证码不能为空");
            }

            String key = "sms:code:" + phoneNumber;
            Object cached = redisUtil.get(key);
            String cachedCode = cached == null ? null : String.valueOf(cached);
            if (!StringUtils.hasText(cachedCode)) {
                throw new BusinessException(ErrorCode.PARAM_ERROR, "验证码已过期或不存在");
            }
            if (!smsCode.equals(cachedCode)) {
                throw new BusinessException(ErrorCode.PARAM_ERROR, "验证码不正确");
            }

            // 验证通过后删除验证码，避免重复使用
            redisUtil.del(key);

            // 查询用户是否存在，不存在则自动注册
            User user = userRepository.findByPhone(phoneNumber).orElse(null);
            if (user == null) {
                String username = "小白_" + IdUtil.simpleUUID();
                // 生成符合规则的随机密码
                String randomPassword = PasswordUtil.generateRandomPassword(12);
                createUser(username, randomPassword, phoneNumber);
                user = userRepository.findByPhone(phoneNumber).orElse(null);
                if (user == null) {
                    throw new BusinessException(ErrorCode.SYSTEM_ERROR, "自动注册失败");
                }
                log.info("手机号 {} 自动注册成功，用户ID: {}", phoneNumber, user.getId());
            }

            // 完成登录
            StpUtil.login(phoneNumber);

            // 更新登录信息
            updateUserLoginInfo(user.getId(), "127.0.0.1"); // TODO: 替换为真实客户端IP

            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("token", StpUtil.getTokenValue());
            result.put("username", user.getUsername());
            result.put("loginId", StpUtil.getLoginId());
            result.put("tokenTimeout", StpUtil.getTokenTimeout());
            result.put("loginTime", System.currentTimeMillis());
            result.put("userId", user.getId());
            result.put("nickname", user.getNickname());

            // 缓存用户登录信息
            cacheUserLoginInfo(phoneNumber, result);

            log.info("手机号 {} 短信验证码登录成功，token: {}", phoneNumber, StpUtil.getTokenValue());
            return result;

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("短信验证码登录/注册异常: ", e);
            throw new BusinessException(ErrorCode.SYSTEM_ERROR, "登录失败: " + e.getMessage());
        }
    }

    /**
     * 用户登出
     * 
     * 清除用户会话和令牌
     * 
     * @param loginId 登录ID
     * @return Boolean 登出结果
     */
    @Override
    public Boolean logout(Object loginId) {
        log.info("用户登出请求，登录ID: {}", loginId);
        
        try {
            if (loginId == null) {
                throw new BusinessException(ErrorCode.PARAM_ERROR, "登录ID不能为空");
            }

            // 执行Sa-Token登出
            StpUtil.logout(loginId);

            // 清除缓存的用户信息
            clearUserCache(loginId.toString());

            log.info("用户 {} 登出成功", loginId);
            return true;

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("用户登出异常: ", e);
            throw new BusinessException(ErrorCode.SYSTEM_ERROR, "登出失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户信息
     * 
     * 根据登录ID获取用户详细信息
     * 
     * @param loginId 登录ID
     */
    @Override
    public Map<String, Object> getUserInfo(Object loginId) {
        log.debug("获取用户信息请求，登录ID: {}", loginId);
        
        try {
            if (loginId == null) {
                throw new BusinessException(ErrorCode.PARAM_ERROR, "登录ID不能为空");
            }

            // 从缓存获取用户信息
            Map<String, Object> userInfo = getCachedUserInfo(loginId.toString());
            
            if (userInfo == null) {
                // 如果缓存中没有，从数据库获取（这里模拟）
                userInfo = loadUserInfoFromDatabase(loginId.toString());
            }

            if (userInfo == null) {
                throw new BusinessException(ErrorCode.RESOURCE_NOT_FOUND, "用户信息不存在");
            }

            // 添加当前登录状态信息
            userInfo.put("isLogin", StpUtil.isLogin(loginId));
            userInfo.put("tokenValue", StpUtil.getTokenValueByLoginId(loginId));
            userInfo.put("tokenTimeout", StpUtil.getTokenTimeout());

            return userInfo;

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("获取用户信息异常: ", e);
            throw new BusinessException(ErrorCode.SYSTEM_ERROR, "获取用户信息失败: " + e.getMessage());
        }
    }

    /**
     * 检查用户登录状态
     * 
     * 验证用户当前的登录状态和令牌有效性
     * 
     * @param loginId 登录ID
     * @return Map<String, Object> 登录状态信息
     */
    @Override
    public Map<String, Object> checkLoginStatus(Object loginId) {
        log.debug("检查用户登录状态，登录ID: {}", loginId);
        
        try {
            Map<String, Object> result = new HashMap<>();
            
            if (loginId == null) {
                result.put("isLogin", false);
                result.put("message", "登录ID为空");
                return result;
            }

            boolean isLogin = StpUtil.isLogin(loginId);
            result.put("isLogin", isLogin);
            result.put("loginId", loginId);

            if (isLogin) {
                result.put("tokenValue", StpUtil.getTokenValueByLoginId(loginId));
                result.put("tokenTimeout", StpUtil.getTokenTimeout());
                result.put("sessionTimeout", StpUtil.getSessionTimeout());
                result.put("message", "用户已登录");
            } else {
                result.put("message", "用户未登录");
            }

            return result;

        } catch (Exception e) {
            log.error("检查用户登录状态异常: ", e);
            throw new BusinessException(ErrorCode.SYSTEM_ERROR, "检查登录状态失败: " + e.getMessage());
        }
    }

    /**
     * 更新用户信息
     * 
     * 更新用户的基本信息
     * 
     * @param loginId 登录ID
     * @param userInfo 要更新的用户信息
     * @return Map<String, Object> 更新结果
     */
    @Override
    public Map<String, Object> updateUserInfo(Object loginId, Map<String, Object> userInfo) {
        log.info("更新用户信息请求，登录ID: {}", loginId);
        
        try {
            if (loginId == null || userInfo == null || userInfo.isEmpty()) {
                throw new BusinessException(ErrorCode.PARAM_ERROR, "参数不能为空");
            }

            // 验证用户是否已登录
            if (!StpUtil.isLogin(loginId)) {
                throw new BusinessException(ErrorCode.UNAUTHORIZED, "用户未登录");
            }

            // 获取当前用户信息
            Map<String, Object> currentUserInfo = getCachedUserInfo(loginId.toString());
            if (currentUserInfo == null) {
                currentUserInfo = new HashMap<>();
            }

            // 更新用户信息
            currentUserInfo.putAll(userInfo);
            currentUserInfo.put("updateTime", System.currentTimeMillis());

            // 保存到缓存
            cacheUserInfo(loginId.toString(), currentUserInfo);

            log.info("用户 {} 信息更新成功", loginId);
            return currentUserInfo;

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("更新用户信息异常: ", e);
            throw new BusinessException(ErrorCode.SYSTEM_ERROR, "更新用户信息失败: " + e.getMessage());
        }
    }

    /**
     * 修改密码
     * 
     * 修改用户密码
     * 
     * @param loginId 登录ID
     * @param oldPassword 旧密码
     * @param newPassword 新密码
     * @return Result<Boolean> 修改结果
     */
    @Override
    public Boolean changePassword(Object loginId, String oldPassword, String newPassword) {
        log.info("修改密码请求，登录ID: {}", loginId);
        
        try {
            if (loginId == null || !StringUtils.hasText(oldPassword) || !StringUtils.hasText(newPassword)) {
                throw new BusinessException(ErrorCode.PARAM_ERROR, "参数不能为空");
            }

            // 验证用户是否已登录
            if (!StpUtil.isLogin(loginId)) {
                throw new BusinessException(ErrorCode.UNAUTHORIZED, "用户未登录");
            }

            // 验证新密码格式
            if (!validatePassword(newPassword)) {
                throw new BusinessException(ErrorCode.PARAM_ERROR, "新密码格式不正确，应为6-20位且包含字母和数字");
            }

            // 验证旧密码
            if (!verifyOldPassword(loginId.toString(), oldPassword)) {
                throw new BusinessException(ErrorCode.PARAM_ERROR, "旧密码不正确");
            }

            // 更新密码
            updatePasswordInDatabase(loginId.toString(), newPassword);

            log.info("用户 {} 密码修改成功", loginId);
            return true;

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("修改密码异常: ", e);
            throw new BusinessException(ErrorCode.SYSTEM_ERROR, "修改密码失败: " + e.getMessage());
        }
    }



    /**
     * 验证用户名格式
     */
    @Override
    public boolean validateUsername(String username) {
        return StringUtils.hasText(username) && USERNAME_PATTERN.matcher(username).matches();
    }

    /**
     * 验证密码强度
     */
    @Override
    public boolean validatePassword(String password) {
        return StringUtils.hasText(password) && PASSWORD_PATTERN.matcher(password).matches();
    }


    /**
     * 检查用户名是否已存在
     */
    @Override
    public boolean isUsernameExists(String username) {
        return userRepository.existsByUsername(username);
    }


    /**
     * 检查手机号是否已存在
     */
    public boolean isPhoneExists(String phone) {
        return userRepository.existsByPhone(phone);
    }


    /**
     * 用户认证
     */
    private boolean authenticateUser(String username, String password) {
        User user = userRepository.findByUsername(username).orElse(null);
        if (user == null) {
            return false;
        }
        return PasswordUtil.matches(password, user.getPassword());
    }

    /**
     * 用户认证并获取用户信息
     */
    private User authenticateAndGetUser(String phone, String password) {
        User user = userRepository.findByPhone(phone).orElse(null);
        if (user == null) {
            return null;
        }
        if (!PasswordUtil.matches(password, user.getPassword())) {
            return null;
        }
        return user;
    }

    /**
     * 更新用户登录信息
     */
    private void updateUserLoginInfo(Long userId, String loginIp) {
        try {
            userRepository.updateLoginInfo(userId, loginIp, java.time.LocalDateTime.now());
            log.debug("用户 {} 登录信息更新成功", userId);
        } catch (Exception e) {
            log.warn("更新用户登录信息失败: {}", e.getMessage());
        }
    }

    /**
     * 创建用户
     */
    private void createUser(String username, String password, String phone) {
        try {
            // 加密密码
            String encodedPassword = PasswordUtil.encodePassword(password);
            
            // 创建用户实体
            User user = User.builder()
                    .username(username)
                    .password(encodedPassword)
                    .phone(phone)
                    .nickname(username) // 默认使用用户名作为昵称
                    .status(User.UserStatus.ACTIVE.getCode())
                    .userType(User.UserType.NORMAL.getCode())
                    .loginCount(0)
                    .deleted(0)
                    .build();
            
            // 保存到数据库
            User savedUser = userRepository.save(user);
            if (savedUser.getId() == null) {
                throw new BusinessException(ErrorCode.SYSTEM_ERROR, "用户创建失败");
            }
            
            log.info("用户创建成功，用户ID: {}, 用户名: {}", savedUser.getId(), username);

            
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("创建用户失败: ", e);
            throw new BusinessException(ErrorCode.SYSTEM_ERROR, "用户创建失败: " + e.getMessage());
        }
    }

    /**
     * 缓存用户登录信息
     */
    private void cacheUserLoginInfo(String phoneNumber, Map<String, Object> loginInfo) {
        try {
            String key = USER_LOGIN_PREFIX + phoneNumber;
            redisUtil.set(key, loginInfo, 24, TimeUnit.HOURS);
        } catch (Exception e) {
            log.warn("缓存用户登录信息失败: {}", e.getMessage());
        }
    }

    /**
     * 缓存用户信息
     */
    private void cacheUserInfo(String loginId, Map<String, Object> userInfo) {
        try {
            String key = USER_INFO_PREFIX + loginId;
            redisUtil.set(key, userInfo, 24, TimeUnit.HOURS);
        } catch (Exception e) {
            log.warn("缓存用户信息失败: {}", e.getMessage());
        }
    }

    /**
     * 获取缓存的用户信息
     */
    @SuppressWarnings("unchecked")
    private Map<String, Object> getCachedUserInfo(String loginId) {
        try {
            String key = USER_INFO_PREFIX + loginId;
            Object cached = redisUtil.get(key);
            return cached instanceof Map ? (Map<String, Object>) cached : null;
        } catch (Exception e) {
            log.warn("获取缓存用户信息失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 清除用户缓存
     */
    private void clearUserCache(String loginId) {
        try {
            redisUtil.del(USER_INFO_PREFIX + loginId);
            redisUtil.del(USER_LOGIN_PREFIX + loginId);
        } catch (Exception e) {
            log.warn("清除用户缓存失败: {}", e.getMessage());
        }
    }

    /**
     * 从数据库加载用户信息
     */
    private Map<String, Object> loadUserInfoFromDatabase(String loginId) {
        try {
            User user = userRepository.findById(Long.valueOf(loginId)).orElse(null);
            if (user == null) {
                return null;
            }
            
            Map<String, Object> userInfo = new HashMap<>();
            userInfo.put("id", user.getId());
            userInfo.put("username", user.getUsername());
            userInfo.put("phone", user.getPhone());
            userInfo.put("nickname", user.getNickname());
            userInfo.put("avatar", user.getAvatar());
            userInfo.put("status", user.getStatus());
            userInfo.put("userType", user.getUserType());
            userInfo.put("lastLoginTime", user.getLastLoginTime());
            userInfo.put("lastLoginIp", user.getLastLoginIp());
            userInfo.put("loginCount", user.getLoginCount());
            userInfo.put("createTime", user.getCreateTime());
            userInfo.put("updateTime", user.getUpdateTime());
            
            return userInfo;
        } catch (Exception e) {
            log.error("从数据库加载用户信息失败: ", e);
            return null;
        }
    }

    /**
     * 验证旧密码
     */
    private boolean verifyOldPassword(String loginId, String oldPassword) {
        try {
            User user = userRepository.findById(Long.valueOf(loginId)).orElse(null);
            if (user == null) {
                return false;
            }
            return PasswordUtil.matches(oldPassword, user.getPassword());
        } catch (Exception e) {
            log.error("验证旧密码失败: ", e);
            return false;
        }
    }

    /**
     * 更新数据库中的密码
     */
    private void updatePasswordInDatabase(String loginId, String newPassword) {
        try {
            String encodedPassword = PasswordUtil.encodePassword(newPassword);
            int result = userRepository.updatePassword(Long.valueOf(loginId), encodedPassword);
            if (result > 0) {
                log.info("用户 {} 密码更新成功", loginId);
            } else {
                log.warn("用户 {} 密码更新失败", loginId);
            }
        } catch (Exception e) {
            log.error("更新用户密码失败: ", e);
            throw new BusinessException(ErrorCode.SYSTEM_ERROR, "密码更新失败");
        }
    }
}
