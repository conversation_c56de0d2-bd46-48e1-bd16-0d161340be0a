package com.ywcx.cx_ai_slightly.service;

import java.util.Map;

/**
 * 用户服务接口
 * 
 * 提供用户相关的业务逻辑处理
 * 包括用户认证、注册、信息管理等功能
 */
public interface UserService {

    /**
     * 用户登录
     * 
     * 验证用户凭据并生成访问令牌
     * 
     * @param username 用户名
     * @param password 密码
     */
    Map<String, Object> login(String username, String password);

    /**
     * 用户注册
     * 
     * 创建新用户账户
     *
     * @param password 密码
     * @param phoneNumber 手机号
     */
    void register(String password, String  phoneNumber);

    /**
     * 短信验证码登录/注册
     * 
     * 用户通过手机号+短信验证码验证后，自动注册并登录（如用户不存在），并返回登录信息
     *
     * @param phoneNumber 手机号
     * @param smsCode 短信验证码
     * @return 登录结果数据（token、loginId等）
     */
    Map<String, Object> loginOrRegisterBySms(String phoneNumber, String smsCode);

    /**
     * 用户登出
     * 
     * 清除用户会话和令牌
     * 
     * @param loginId 登录ID
     * @return Boolean 登出结果
     */
    Boolean logout(Object loginId);

    /**
     * 获取用户信息
     * 
     * 根据登录ID获取用户详细信息
     * 
     * @param loginId 登录ID
     */
    Map<String, Object> getUserInfo(Object loginId);

    /**
     * 检查用户登录状态
     * 
     * 验证用户当前的登录状态和令牌有效性
     * 
     * @param loginId 登录ID
     * @return Result<Map<String, Object>> 登录状态信息
     */
    Map<String, Object> checkLoginStatus(Object loginId);

    /**
     * 更新用户信息
     * 
     * 更新用户的基本信息
     * 
     * @param loginId 登录ID
     * @param userInfo 要更新的用户信息
     * @return Map<String, Object> 更新结果
     */
    Map<String, Object> updateUserInfo(Object loginId, Map<String, Object> userInfo);

    /**
     * 修改密码
     * 
     * 修改用户密码
     * 
     * @param loginId 登录ID
     * @param oldPassword 旧密码
     * @param newPassword 新密码
     * @return Boolean 修改结果
     */
    Boolean changePassword(Object loginId, String oldPassword, String newPassword);

    /**
     * 验证用户名格式
     * 
     * @param username 用户名
     * @return boolean true表示格式正确
     */
    boolean validateUsername(String username);

    /**
     * 验证密码强度
     * 
     * @param password 密码
     * @return boolean true表示密码强度符合要求
     */
    boolean validatePassword(String password);


    /**
     * 检查用户名是否已存在
     * 
     * @param username 用户名
     * @return boolean true表示已存在
     */
    boolean isUsernameExists(String username);

}
