package com.ywcx.cx_ai_slightly.service;

import jakarta.servlet.http.HttpServletRequest;

/**
 * 验证码服务接口
 */
public interface CaptchaService {
    
    /**
     * 生成验证码
     * @param sessionId 会话ID
     * @return 验证码图片的Base64编码
     */
    String generateCaptcha(String sessionId);
    
    /**
     * 验证验证码
     * @param sessionId 会话ID
     * @param captcha 用户输入的验证码
     * @return 验证结果
     */
    boolean verifyCaptcha(String sessionId, String captcha);
    
    /**
     * 检查是否需要验证码
     * @param request HTTP请求
     * @return 是否需要验证码
     */
    boolean requiresCaptcha(HttpServletRequest request);
    
    /**
     * 清除验证码
     * @param sessionId 会话ID
     */
    void clearCaptcha(String sessionId);
}