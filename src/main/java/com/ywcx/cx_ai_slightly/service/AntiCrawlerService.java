package com.ywcx.cx_ai_slightly.service;

import jakarta.servlet.http.HttpServletRequest;

/**
 * 反爬虫服务接口
 */
public interface AntiCrawlerService {
    
    /**
     * 检查请求是否被允许
     * @param request HTTP请求
     * @return true表示允许，false表示拒绝
     */
    boolean isRequestAllowed(HttpServletRequest request);
    
    /**
     * 记录请求
     * @param request HTTP请求
     */
    void recordRequest(HttpServletRequest request);
    
    /**
     * 检查IP是否被封禁
     * @param ip IP地址
     * @return true表示被封禁
     */
    boolean isIpBanned(String ip);
    
    /**
     * 封禁IP
     * @param ip IP地址
     * @param durationSeconds 封禁时长（秒）
     */
    void banIp(String ip, int durationSeconds);
    
    /**
     * 解封IP
     * @param ip IP地址
     */
    void unbanIp(String ip);
    
    /**
     * 获取客户端真实IP
     * @param request HTTP请求
     * @return IP地址
     */
    String getClientIp(HttpServletRequest request);
}