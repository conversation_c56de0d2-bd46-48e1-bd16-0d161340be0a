package com.ywcx.cx_ai_slightly.controller;

import com.ywcx.cx_ai_slightly.common.Result;
import com.ywcx.cx_ai_slightly.entity.resp.AntiCrawlerTestResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 反爬虫测试控制器
 * 
 * 提供用于测试反爬虫功能的接口
 */
@Slf4j
@RestController
@RequestMapping("/api/test")
@Tag(name = "反爬虫测试", description = "用于测试反爬虫功能的接口")
public class AntiCrawlerTestController {
    
    /**
     * 普通测试接口
     * 
     * 用于测试反爬虫拦截的普通接口
     * 
     * @param request HTTP请求对象
     * @return Result 测试结果
     */
    @Operation(summary = "普通测试接口", description = "用于测试反爬虫拦截的普通接口")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "请求成功",
            content = @Content(mediaType = "application/json",
                examples = @ExampleObject(value = "{\"code\":200,\"message\":\"成功\",\"data\":{\"message\":\"请求成功\",\"clientIp\":\"***********\",\"userAgent\":\"Mozilla/5.0...\"}}"))),
        @ApiResponse(responseCode = "500", description = "请求失败",
            content = @Content(mediaType = "application/json",
                examples = @ExampleObject(value = "{\"code\":500,\"message\":\"测试接口异常: 错误信息\"}")))
    })
    @GetMapping("/normal")
    public Result<AntiCrawlerTestResponse> normalTest(HttpServletRequest request) {
        try {
            AntiCrawlerTestResponse response = AntiCrawlerTestResponse.builder()
                    .message("请求成功")
                    .clientIp(getClientIp(request))
                    .userAgent(request.getHeader("User-Agent"))
                    .build();
            
            return Result.success(response);
        } catch (Exception e) {
            log.error("普通测试接口异常", e);
            return Result.error(500, "测试接口异常: " + e.getMessage());
        }
    }
    
    /**
     * 高频测试接口
     * 
     * 用于测试频率限制的接口
     * 
     * @param request HTTP请求对象
     * @return Result 测试结果
     */
    @Operation(summary = "高频测试接口", description = "用于测试频率限制的接口")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "请求成功",
            content = @Content(mediaType = "application/json",
                examples = @ExampleObject(value = "{\"code\":200,\"message\":\"成功\",\"data\":{\"message\":\"高频请求测试成功\",\"clientIp\":\"***********\"}}"))),
        @ApiResponse(responseCode = "500", description = "请求失败",
            content = @Content(mediaType = "application/json",
                examples = @ExampleObject(value = "{\"code\":500,\"message\":\"测试接口异常: 错误信息\"}")))
    })
    @GetMapping("/high-frequency")
    public Result<AntiCrawlerTestResponse> highFrequencyTest(HttpServletRequest request) {
        try {
            AntiCrawlerTestResponse response = AntiCrawlerTestResponse.builder()
                    .message("高频请求测试成功")
                    .clientIp(getClientIp(request))
                    .build();
            
            return Result.success(response);
        } catch (Exception e) {
            log.error("高频测试接口异常", e);
            return Result.error(500, "测试接口异常: " + e.getMessage());
        }
    }
    
    /**
     * 需要验证码的测试接口
     * 
     * 模拟需要验证码验证的接口
     * 
     * @param request HTTP请求对象
     * @return Result 测试结果
     */
    @Operation(summary = "需要验证码的测试接口", description = "模拟需要验证码验证的接口")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "验证通过",
            content = @Content(mediaType = "application/json",
                examples = @ExampleObject(value = "{\"code\":200,\"message\":\"成功\",\"data\":{\"message\":\"验证码验证通过\",\"clientIp\":\"***********\"}}"))),
        @ApiResponse(responseCode = "500", description = "验证失败",
            content = @Content(mediaType = "application/json",
                examples = @ExampleObject(value = "{\"code\":500,\"message\":\"测试接口异常: 错误信息\"}")))
    })
    @GetMapping("/captcha-required")
    public Result<AntiCrawlerTestResponse> captchaRequiredTest(HttpServletRequest request) {
        try {
            AntiCrawlerTestResponse response = AntiCrawlerTestResponse.builder()
                    .message("验证码验证通过")
                    .clientIp(getClientIp(request))
                    .build();
            
            return Result.success(response);
        } catch (Exception e) {
            log.error("验证码测试接口异常", e);
            return Result.error(500, "测试接口异常: " + e.getMessage());
        }
    }
    
    /**
     * 获取客户端IP
     */
    private String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("X-Real-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip != null ? ip : "unknown";
    }
}
