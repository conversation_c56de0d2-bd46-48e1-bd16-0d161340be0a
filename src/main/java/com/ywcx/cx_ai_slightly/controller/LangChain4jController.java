package com.ywcx.cx_ai_slightly.controller;

import com.ywcx.cx_ai_slightly.common.Result;
import com.ywcx.cx_ai_slightly.entity.request.SimpleChatRequest;
import com.ywcx.cx_ai_slightly.entity.request.WeatherChatRequest;
import com.ywcx.cx_ai_slightly.service.AiChatService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;

/**
 * LangChain4j AI对话控制器
 * 
 * 提供基于LangChain4j框架的AI对话服务
 * 包括简单对话和天气查询等功能
 */
@RestController
@RequestMapping("/api/ai")
@Slf4j
@RequiredArgsConstructor
@Tag(name = "AI对话服务", description = "基于LangChain4j框架的AI对话相关接口")
public class LangChain4jController {

    private final AiChatService aiChatService;

    /**
     * 简单对话接口
     * 
     * 处理用户的简单对话请求，调用AI模型生成回复
     * 
     * @param request 简单对话请求对象，包含用户消息
     * @return Result<String> AI生成的回复内容
     */
    @Operation(summary = "简单对话", description = "处理用户的简单对话请求，调用AI模型生成回复")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "对话成功",
            content = @Content(mediaType = "application/json",
                examples = @ExampleObject(value = "{\"code\":200,\"message\":\"成功\",\"data\":\"AI回复内容\"}"))),
        @ApiResponse(responseCode = "500", description = "对话处理失败")
    })
    @PostMapping("/simple")
    public Result<String> simpleChat(
        @Parameter(description = "简单对话请求", required = true,
            content = @Content(examples = @ExampleObject(value = "{\"message\":\"你好，请介绍一下自己\"}")))
        @RequestBody SimpleChatRequest request) {
        log.info("收到简单对话请求，消息长度: {}", 
                request.getMessage() != null ? request.getMessage().length() : 0);
        
        try {
            // 调用AI聊天服务处理简单对话
            Result<String> result = aiChatService.simpleChat(request.getMessage());
            
            log.info("简单对话处理完成，响应状态: {}", result.getCode());
            return result;
            
        } catch (Exception e) {
            log.error("简单对话处理异常: ", e);
            return Result.error(500, "对话处理失败: " + e.getMessage());
        }
    }

    /**
     * 天气查询对话接口
     * 
     * 处理用户的天气查询请求，生成创意性的天气描述
     * 
     * @param request 天气查询请求对象，包含城市名称
     * @return Result<String> AI生成的天气描述
     */
    @Operation(summary = "天气查询对话", description = "处理用户的天气查询请求，生成创意性的天气描述")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "天气查询成功",
            content = @Content(mediaType = "application/json",
                examples = @ExampleObject(value = "{\"code\":200,\"message\":\"成功\",\"data\":\"北京今天天气晴朗，温度适宜...\"}"))),
        @ApiResponse(responseCode = "500", description = "天气查询失败")
    })
    @PostMapping("/weather")
    public Result<String> weatherChat(
        @Parameter(description = "天气查询请求", required = true,
            content = @Content(examples = @ExampleObject(value = "{\"city\":\"北京\"}")))
        @RequestBody WeatherChatRequest request) {
        log.info("收到天气查询请求，城市: {}", request.getCity());
        
        try {
            // 调用AI聊天服务处理天气查询
            Result<String> result = aiChatService.weatherChat(request.getCity());
            
            log.info("天气查询处理完成，响应状态: {}", result.getCode());
            return result;
            
        } catch (Exception e) {
            log.error("天气查询处理异常: ", e);
            return Result.error(500, "天气查询失败: " + e.getMessage());
        }
    }

    /**
     * 获取AI模型状态接口
     * 
     * 返回当前AI模型的状态信息
     * 
     * @return Result AI模型状态
     */
    @Operation(summary = "获取AI模型状态", description = "返回当前AI模型的状态信息")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "获取状态成功",
            content = @Content(mediaType = "application/json",
                examples = @ExampleObject(value = "{\"code\":200,\"message\":\"成功\",\"data\":\"模型运行正常\"}"))),
        @ApiResponse(responseCode = "500", description = "获取状态失败")
    })
    @GetMapping("/status")
    public Result<String> getModelStatus() {
        log.debug("收到获取AI模型状态请求");
        
        try {
            // 调用AI聊天服务获取模型状态
            Result<String> result = aiChatService.getModelStatus();
            
            log.debug("AI模型状态获取完成");
            return result;
            
        } catch (Exception e) {
            log.error("获取AI模型状态异常: ", e);
            return Result.error(500, "获取模型状态失败: " + e.getMessage());
        }
    }

    /**
     * 清理对话历史接口
     * 
     * 清理指定用户的对话历史记录
     * 
     * @param userId 用户ID
     * @return Result 清理结果
     */
    @Operation(summary = "清理对话历史", description = "清理指定用户的对话历史记录")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "清理成功",
            content = @Content(mediaType = "application/json",
                examples = @ExampleObject(value = "{\"code\":200,\"message\":\"成功\",\"data\":true}"))),
        @ApiResponse(responseCode = "500", description = "清理失败")
    })
    @DeleteMapping("/history/{userId}")
    public Result<Boolean> clearChatHistory(
        @Parameter(description = "用户ID", required = true, example = "user123")
        @PathVariable String userId) {
        log.info("收到清理对话历史请求，用户ID: {}", userId);
        
        try {
            // 调用AI聊天服务清理对话历史
            Result<Boolean> result = aiChatService.clearChatHistory(userId);
            
            log.info("对话历史清理完成，用户ID: {}", userId);
            return result;
            
        } catch (Exception e) {
            log.error("清理对话历史异常: ", e);
            return Result.error(500, "清理对话历史失败: " + e.getMessage());
        }
    }

    /**
     * 流式对话接口
     *
     * @param request 简单对话请求对象，包含用户消息
     * @return Flux<String> AI生成的回复SSE流
     */
    @Operation(summary = "流式对话", description = "处理用户的流式对话请求，以SSE形式返回AI模型回复")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "对话成功",
                    content = @Content(mediaType = "text/event-stream")),
            @ApiResponse(responseCode = "500", description = "对话处理失败")
    })
    @PostMapping(value = "/streaming-chat", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<String> streamingChat(@RequestBody SimpleChatRequest request) {
        log.info("收到流式对话请求，消息长度: {}",
                request.getMessage() != null ? request.getMessage().length() : 0);
        return aiChatService.streamingChat(request.getMessage());
    }
}
