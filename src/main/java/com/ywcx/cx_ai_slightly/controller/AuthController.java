package com.ywcx.cx_ai_slightly.controller;

import cn.dev33.satoken.stp.StpUtil;
import cn.dev33.satoken.util.SaResult;
import com.ywcx.cx_ai_slightly.entity.request.auth.LoginRequest;
import com.ywcx.cx_ai_slightly.entity.request.auth.RegisterRequest;
import com.ywcx.cx_ai_slightly.entity.request.auth.SmsLoginRequest;
import com.ywcx.cx_ai_slightly.entity.resp.auth.TokenCheckResponse;
import com.ywcx.cx_ai_slightly.entity.resp.auth.UserInfoResponse;
import com.ywcx.cx_ai_slightly.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.Map;

;


/**
 * 用户认证控制器
 * 
 * 提供用户登录、注册、登出等认证相关功能
 * 使用Sa-Token框架进行权限管理和会话控制
 */
@RestController
@RequestMapping("/api/auth")
@Slf4j
@RequiredArgsConstructor
@Tag(name = "用户认证", description = "用户登录、注册、登出等认证相关接口")
public class AuthController {

    private final UserService userService;

    /**
     * 用户登录接口
     * 
     * 验证用户名和密码，成功后创建会话并返回Token信息
     * 
     * @param loginRequest 登录请求数据，包含用户名和密码
     * @return SaResult 登录结果，成功时包含token、username、loginId等信息
     */
    @Operation(summary = "用户登录", description = "验证用户名和密码，成功后创建会话并返回Token信息")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "登录成功",
            content = @Content(mediaType = "application/json",
                examples = @ExampleObject(value = "{\"code\":200,\"msg\":\"登录成功\",\"data\":{\"token\":\"xxx\",\"loginId\":\"user123\"}}"))),
        @ApiResponse(responseCode = "400", description = "登录失败",
            content = @Content(mediaType = "application/json",
                examples = @ExampleObject(value = "{\"code\":400,\"msg\":\"手机号或密码错误\"}")))
    })
    @PostMapping("/login")
    public SaResult login(
        @Parameter(description = "登录请求数据", required = true)
        @Valid @RequestBody LoginRequest loginRequest) {
        String phoneNumber = loginRequest.getPhoneNumber();
        String password = loginRequest.getPassword();
        
        try {
            // 调用UserService进行登录验证
            Map<String, Object> login = userService.login(phoneNumber, password);

            return SaResult.ok("登录成功").setData(login);
        } catch (Exception e) {
            log.error("用户 {} 登录异常: ", phoneNumber, e);
            return SaResult.error("登录失败: " + e.getMessage());
        }
    }

    /**
     * 短信验证码登录/注册接口
     *
     * 用户通过手机号 + 短信验证码验证，验证通过后：
     * - 若用户不存在则自动注册（默认字段填充）
     * - 自动完成登录并返回token等信息
     */
    @Operation(summary = "短信验证码登录/注册", description = "验证短信验证码，自动注册并登录用户")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "登录成功",
            content = @Content(mediaType = "application/json",
                examples = @ExampleObject(value = "{\"code\":200,\"msg\":\"登录成功\",\"data\":{\"token\":\"xxx\",\"loginId\":\"13800138000\"}}"))),
        @ApiResponse(responseCode = "400", description = "验证码错误或过期",
            content = @Content(mediaType = "application/json",
                examples = @ExampleObject(value = "{\"code\":400,\"msg\":\"验证码错误\"}")))
    })
    @PostMapping("/sms-login")
    public SaResult smsLogin(
        @Parameter(description = "短信登录请求数据", required = true)
        @Valid @RequestBody SmsLoginRequest request) {
        String phoneNumber = request.getPhoneNumber();
        String code = request.getCode();

        try {
            Map<String, Object> data = userService.loginOrRegisterBySms(phoneNumber, code);
            return SaResult.ok("登录成功").setData(data);
        } catch (Exception e) {
            log.error("短信验证码登录异常，手机号 {}: ", phoneNumber, e);
            return SaResult.error(e.getMessage());
        }
    }

    /**
     * 用户注册接口
     * 
     * 处理新用户注册请求
     * 
     * @param registerRequest 注册请求数据，包含用户名、密码和确认密码
     * @return SaResult 注册结果
     */
    @Operation(summary = "用户注册", description = "处理新用户注册请求")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "注册成功",
            content = @Content(mediaType = "application/json",
                examples = @ExampleObject(value = "{\"code\":200,\"msg\":\"注册成功\",\"data\": null}"))),
        @ApiResponse(responseCode = "400", description = "注册失败，用户名已存在或参数错误",
            content = @Content(mediaType = "application/json",
                examples = @ExampleObject(value = "{\"code\":400,\"msg\":\"用户名已存在\"}")))
    })
    @PostMapping("/register")
    public SaResult register(
        @Parameter(description = "注册请求数据", required = true)
        @Valid @RequestBody RegisterRequest registerRequest) {
        String phoneNumber = registerRequest.getPhoneNumber();
        String password = registerRequest.getPassword();
        String confirmPassword = registerRequest.getConfirmPassword();
        
        // 验证两次输入的密码是否一致
        if (!password.equals(confirmPassword)) {
            return SaResult.error("两次输入的密码不一致");
        }
        
        try {
            userService.register(password, phoneNumber);

            log.info("用户 {} 注册成功", phoneNumber);
            return SaResult.ok("注册成功");
        } catch (Exception e) {
            log.error("用户注册异常: ", e);
            return SaResult.error(e.getMessage());
        }
    }

    /**
     * 用户登出接口
     * 
     * 清除当前用户的会话信息，使Token失效
     * 
     * @return SaResult 登出结果
     */
    @Operation(summary = "用户登出", description = "清除当前用户的会话信息，使Token失效")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "登出成功"),
        @ApiResponse(responseCode = "401", description = "用户未登录")
    })
    @PostMapping("/logout")
    public SaResult logout() {
        Object loginId = StpUtil.getLoginId();
        
        // 执行登出操作，清除会话
        StpUtil.logout();
        
        log.info("用户 {} 登出成功", loginId);
        return SaResult.ok("登出成功");
    }

    /**
     * 获取用户信息接口
     * 
     * 返回当前登录用户的基本信息和会话状态
     * 需要用户已登录才能访问
     * 
     * @return SaResult 用户信息，包含用户详细资料和登录状态
     */
    @Operation(summary = "获取用户信息", description = "返回当前登录用户的基本信息和会话状态，需要用户已登录")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "获取成功",
            content = @Content(mediaType = "application/json",
                examples = @ExampleObject(value = "{\"code\":200,\"msg\":\"获取用户信息成功\",\"data\":{\"id\":1,\"username\":\"user123\",\"nickname\":\"张三\",\"email\":\"<EMAIL>\",\"phone\":\"13800138000\",\"avatar\":\"https://example.com/avatar.jpg\",\"status\":1,\"userType\":1,\"lastLoginTime\":\"2024-01-01 12:00:00\",\"lastLoginIp\":\"***********\",\"loginCount\":10,\"createTime\":\"2024-01-01 10:00:00\",\"updateTime\":\"2024-01-01 12:00:00\",\"loginId\":\"user123\",\"tokenValue\":\"xxx\",\"isLogin\":true,\"tokenTimeout\":2592000,\"sessionTimeout\":2592000}}"))),
        @ApiResponse(responseCode = "401", description = "用户未登录")
    })
    @GetMapping("/userinfo")
    public SaResult getUserInfo(@RequestHeader String token) {
        // 获取当前登录用户ID
        Object loginId = StpUtil.getLoginId(token);
        
        // 通过UserService获取用户详细信息
        Map<String, Object> res = userService.getUserInfo(loginId);

        // 构建用户信息响应
        UserInfoResponse userInfo = UserInfoResponse.builder()
                .username((String) res.get("username"))
                .nickname((String) res.get("nickname"))
                .email((String) res.get("email"))
                .phone((String) res.get("phone"))
                .avatar((String) res.get("avatar"))
                .status(res.get("status") != null ? Integer.valueOf(res.get("status").toString()) : null)
                .userType(res.get("userType") != null ? Integer.valueOf(res.get("userType").toString()) : null)
                .lastLoginTime((LocalDateTime) res.get("lastLoginTime"))
                .lastLoginIp((String) res.get("lastLoginIp"))
                .loginCount(res.get("loginCount") != null ? Integer.valueOf(res.get("loginCount").toString()) : null)
                .createTime((LocalDateTime) res.get("createTime"))
                .updateTime((LocalDateTime) res.get("updateTime"))
                .build();
        
        log.info("获取用户 {} 的信息", loginId);
        return SaResult.ok("获取用户信息成功").setData(userInfo);
    }

    /**
     * Token有效性检查接口
     * 
     * 检查当前Token是否有效，返回登录状态和相关信息
     * 可用于前端判断用户登录状态
     * 
     * @return SaResult Token检查结果，包含isLogin状态和相关信息
     */
    @Operation(summary = "Token有效性检查", description = "检查当前Token是否有效，返回登录状态和相关信息")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "检查完成",
            content = @Content(mediaType = "application/json",
                examples = @ExampleObject(value = "{\"code\":200,\"msg\":\"Token检查完成\",\"data\":{\"isLogin\":true,\"loginId\":\"user123\",\"tokenValue\":\"xxx\"}}")))
    })
    @GetMapping("/check")
    public SaResult checkToken() {
        boolean isLogin = StpUtil.isLogin();
        
        TokenCheckResponse response;
        if (isLogin) {
            response = TokenCheckResponse.builder()
                    .isLogin(true)
                    .loginId(String.valueOf(StpUtil.getLoginId()))
                    .tokenValue(StpUtil.getTokenValue())
                    .tokenTimeout(StpUtil.getTokenTimeout())
                    .build();
            log.debug("Token检查：用户 {} 已登录", StpUtil.getLoginId());
        } else {
            response = TokenCheckResponse.builder()
                    .isLogin(false)
                    .build();
            log.debug("Token检查：用户未登录");
        }
        
        return SaResult.ok("Token检查完成").setData(response);
    }
}
