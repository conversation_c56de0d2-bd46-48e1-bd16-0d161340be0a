package com.ywcx.cx_ai_slightly.controller;

import com.ywcx.cx_ai_slightly.common.Result;
import com.ywcx.cx_ai_slightly.entity.resp.captcha.CaptchaResponse;
import com.ywcx.cx_ai_slightly.entity.resp.captcha.CaptchaVerifyResponse;
import com.ywcx.cx_ai_slightly.service.CaptchaService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.UUID;

;
;

/**
 * 验证码控制器
 * 
 * 提供图形验证码的生成、验证和刷新功能
 */
@Slf4j
@RestController
@RequestMapping("/api/captcha")
@Tag(name = "验证码管理", description = "验证码相关接口")
public class CaptchaController {
    
    @Autowired
    private CaptchaService captchaService;
    
    /**
     * 生成验证码
     * 
     * 生成一个新的图形验证码，并返回Base64编码的图片数据
     * 
     * @param request HTTP请求对象
     * @return Result 包含sessionId和captchaImage的响应结果
     */
    @Operation(summary = "生成验证码", description = "生成图形验证码")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "验证码生成成功",
            content = @Content(mediaType = "application/json",
                examples = @ExampleObject(value = "{\"code\":200,\"message\":\"成功\",\"data\":{\"sessionId\":\"uuid-string\",\"captchaImage\":\"data:image/png;base64,xxx\"}}"))),
        @ApiResponse(responseCode = "500", description = "验证码生成失败",
            content = @Content(mediaType = "application/json",
                examples = @ExampleObject(value = "{\"code\":500,\"message\":\"生成验证码失败: 错误信息\"}")))
    })
    @GetMapping("/generate")
    public Result<CaptchaResponse> generateCaptcha(HttpServletRequest request) {
        try {
            // 生成会话ID
            String sessionId = UUID.randomUUID().toString();
            
            // 生成验证码
            String captchaImage = captchaService.generateCaptcha(sessionId);
            
            CaptchaResponse response = CaptchaResponse.builder()
                    .sessionId(sessionId)
                    .captchaImage(captchaImage)
                    .build();
            
            return Result.success(response);
        } catch (Exception e) {
            log.error("生成验证码失败", e);
            return Result.error(500, "生成验证码失败: " + e.getMessage());
        }
    }
    
    /**
     * 验证验证码
     * 
     * 验证用户输入的验证码是否正确
     * 
     * @param sessionId 会话ID
     * @param captcha 用户输入的验证码
     * @return Result 验证结果
     */
    @Operation(summary = "验证验证码", description = "验证用户输入的验证码")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "验证码验证完成",
            content = @Content(mediaType = "application/json",
                examples = @ExampleObject(value = "{\"code\":200,\"message\":\"成功\",\"data\":{\"valid\":true,\"message\":\"验证成功\"}}"))),
        @ApiResponse(responseCode = "500", description = "验证码验证失败",
            content = @Content(mediaType = "application/json",
                examples = @ExampleObject(value = "{\"code\":500,\"message\":\"验证验证码失败: 错误信息\"}")))
    })
    @PostMapping("/verify")
    public Result<CaptchaVerifyResponse> verifyCaptcha(
            @Parameter(description = "会话ID", required = true, example = "550e8400-e29b-41d4-a716-************")
            @RequestParam String sessionId,
            @Parameter(description = "用户输入的验证码", required = true, example = "ABCD")
            @RequestParam String captcha) {
        
        try {
            boolean isValid = captchaService.verifyCaptcha(sessionId, captcha);
            
            CaptchaVerifyResponse response = CaptchaVerifyResponse.builder()
                    .valid(isValid)
                    .message(isValid ? "验证成功" : "验证码错误或已过期")
                    .build();
            
            return Result.success(response);
        } catch (Exception e) {
            log.error("验证验证码失败", e);
            return Result.error(500, "验证验证码失败: " + e.getMessage());
        }
    }
    
    /**
     * 刷新验证码
     * 
     * 清除旧验证码并生成新的验证码图片
     * 
     * @param sessionId 会话ID
     * @return Result 包含新sessionId和captchaImage的响应结果
     */
    @Operation(summary = "刷新验证码", description = "刷新验证码图片")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "验证码刷新成功",
            content = @Content(mediaType = "application/json",
                examples = @ExampleObject(value = "{\"code\":200,\"message\":\"成功\",\"data\":{\"sessionId\":\"uuid-string\",\"captchaImage\":\"data:image/png;base64,xxx\"}}"))),
        @ApiResponse(responseCode = "500", description = "验证码刷新失败",
            content = @Content(mediaType = "application/json",
                examples = @ExampleObject(value = "{\"code\":500,\"message\":\"刷新验证码失败: 错误信息\"}")))
    })
    @PostMapping("/refresh")
    public Result<CaptchaResponse> refreshCaptcha(
            @Parameter(description = "会话ID", required = true, example = "550e8400-e29b-41d4-a716-************")
            @RequestParam String sessionId) {
        try {
            // 清除旧验证码
            captchaService.clearCaptcha(sessionId);
            
            // 生成新验证码
            String captchaImage = captchaService.generateCaptcha(sessionId);
            
            CaptchaResponse response = CaptchaResponse.builder()
                    .sessionId(sessionId)
                    .captchaImage(captchaImage)
                    .build();
            
            return Result.success(response);
        } catch (Exception e) {
            log.error("刷新验证码失败", e);
            return Result.error(500, "刷新验证码失败: " + e.getMessage());
        }
    }
}
