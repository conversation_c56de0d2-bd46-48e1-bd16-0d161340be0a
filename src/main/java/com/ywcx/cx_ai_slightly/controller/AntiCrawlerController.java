package com.ywcx.cx_ai_slightly.controller;

import com.ywcx.cx_ai_slightly.common.Result;
import com.ywcx.cx_ai_slightly.entity.resp.CurrentIpResponse;
import com.ywcx.cx_ai_slightly.entity.resp.IpBanResponse;
import com.ywcx.cx_ai_slightly.entity.resp.IpUnbanResponse;
import com.ywcx.cx_ai_slightly.entity.resp.anticrawler.IpStatusResponse;
import com.ywcx.cx_ai_slightly.service.AntiCrawlerService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

;

/**
 * 反爬虫管理控制器
 * 
 * 提供IP封禁、解封和状态检查等反爬虫管理功能
 */
@Slf4j
@RestController
@RequestMapping("/api/admin/anti-crawler")
@Tag(name = "反爬虫管理", description = "反爬虫策略管理接口")
public class AntiCrawlerController {
    
    @Autowired
    private AntiCrawlerService antiCrawlerService;
    
    /**
     * 检查IP状态
     * 
     * 检查指定IP地址是否被封禁
     * 
     * @param ip 要检查的IP地址
     * @return Result IP状态信息
     */
    @Operation(summary = "检查IP状态", description = "检查指定IP的封禁状态")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "状态检查成功",
            content = @Content(mediaType = "application/json",
                examples = @ExampleObject(value = "{\"code\":200,\"message\":\"成功\",\"data\":{\"ip\":\"***********\",\"banned\":false}}"))),
        @ApiResponse(responseCode = "500", description = "状态检查失败",
            content = @Content(mediaType = "application/json",
                examples = @ExampleObject(value = "{\"code\":500,\"message\":\"检查IP状态失败: 错误信息\"}")))
    })
    @GetMapping("/ip/status")
    public Result<IpStatusResponse> checkIpStatus(
            @Parameter(description = "IP地址", required = true, example = "***********")
            @RequestParam String ip) {
        try {
            boolean isBanned = antiCrawlerService.isIpBanned(ip);
            
            IpStatusResponse response = IpStatusResponse.builder()
                    .ip(ip)
                    .banned(isBanned)
                    .build();
            
            return Result.success(response);
        } catch (Exception e) {
            log.error("检查IP状态失败", e);
            return Result.error(500, "检查IP状态失败: " + e.getMessage());
        }
    }
    
    /**
     * 封禁IP
     * 
     * 手动封禁指定的IP地址
     * 
     * @param ip 要封禁的IP地址
     * @param durationSeconds 封禁时长（秒），默认3600秒（1小时）
     * @return Result 封禁操作结果
     */
    @Operation(summary = "封禁IP", description = "手动封禁指定IP地址")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "IP封禁成功",
            content = @Content(mediaType = "application/json",
                examples = @ExampleObject(value = "{\"code\":200,\"message\":\"成功\",\"data\":{\"message\":\"IP封禁成功\",\"ip\":\"***********\",\"duration\":3600}}"))),
        @ApiResponse(responseCode = "500", description = "IP封禁失败",
            content = @Content(mediaType = "application/json",
                examples = @ExampleObject(value = "{\"code\":500,\"message\":\"封禁IP失败: 错误信息\"}")))
    })
    @PostMapping("/ip/ban")
    public Result<IpBanResponse> banIp(
            @Parameter(description = "要封禁的IP地址", required = true, example = "***********")
            @RequestParam String ip,
            @Parameter(description = "封禁时长（秒），默认3600秒（1小时）", example = "3600")
            @RequestParam(defaultValue = "3600") int durationSeconds) {
        
        try {
            antiCrawlerService.banIp(ip, durationSeconds);
            
            IpBanResponse response = IpBanResponse.builder()
                    .message("IP封禁成功")
                    .ip(ip)
                    .duration(durationSeconds)
                    .build();
            
            return Result.success(response);
        } catch (Exception e) {
            log.error("封禁IP失败", e);
            return Result.error(500, "封禁IP失败: " + e.getMessage());
        }
    }
    
    /**
     * 解封IP
     * 
     * 解除指定IP地址的封禁状态
     * 
     * @param ip 要解封的IP地址
     * @return Result 解封操作结果
     */
    @Operation(summary = "解封IP", description = "解除指定IP的封禁状态")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "IP解封成功",
            content = @Content(mediaType = "application/json",
                examples = @ExampleObject(value = "{\"code\":200,\"message\":\"成功\",\"data\":{\"message\":\"IP解封成功\",\"ip\":\"***********\"}}"))),
        @ApiResponse(responseCode = "500", description = "IP解封失败",
            content = @Content(mediaType = "application/json",
                examples = @ExampleObject(value = "{\"code\":500,\"message\":\"解封IP失败: 错误信息\"}")))
    })
    @PostMapping("/ip/unban")
    public Result<IpUnbanResponse> unbanIp(
            @Parameter(description = "要解封的IP地址", required = true, example = "***********")
            @RequestParam String ip) {
        try {
            antiCrawlerService.unbanIp(ip);
            
            IpUnbanResponse response = IpUnbanResponse.builder()
                    .message("IP解封成功")
                    .ip(ip)
                    .build();
            
            return Result.success(response);
        } catch (Exception e) {
            log.error("解封IP失败", e);
            return Result.error(500, "解封IP失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取当前客户端IP
     * 
     * 获取当前请求的客户端IP地址及相关信息
     * 
     * @param request HTTP请求对象
     * @return Result 客户端IP信息
     */
    @Operation(summary = "获取当前IP", description = "获取当前请求的客户端IP地址")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "获取IP成功",
            content = @Content(mediaType = "application/json",
                examples = @ExampleObject(value = "{\"code\":200,\"message\":\"成功\",\"data\":{\"ip\":\"***********\",\"banned\":false,\"userAgent\":\"Mozilla/5.0...\"}}"))),
        @ApiResponse(responseCode = "500", description = "获取IP失败",
            content = @Content(mediaType = "application/json",
                examples = @ExampleObject(value = "{\"code\":500,\"message\":\"获取当前IP失败: 错误信息\"}")))
    })
    @GetMapping("/ip/current")
    public Result<CurrentIpResponse> getCurrentIp(HttpServletRequest request) {
        try {
            String clientIp = antiCrawlerService.getClientIp(request);
            boolean isBanned = antiCrawlerService.isIpBanned(clientIp);
            
            CurrentIpResponse response = CurrentIpResponse.builder()
                    .ip(clientIp)
                    .banned(isBanned)
                    .userAgent(request.getHeader("User-Agent"))
                    .build();
            
            return Result.success(response);
        } catch (Exception e) {
            log.error("获取当前IP失败", e);
            return Result.error(500, "获取当前IP失败: " + e.getMessage());
        }
    }
}
