package com.ywcx.cx_ai_slightly.controller;

import com.ywcx.cx_ai_slightly.service.AliSmsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 短信服务控制器
 * 
 * 提供短信验证码发送功能
 */
@RestController
@RequestMapping("/api/sms")
@Slf4j
@Tag(name = "短信服务", description = "短信验证码相关接口")
public class SmsController {

    @Autowired
    private AliSmsService aliSmsService;

    /**
     * 发送短信验证码接口
     * 
     * @param phoneNumber 手机号
     * @return 发送结果
     */
    @Operation(summary = "发送短信验证码", description = "根据手机号发送短信验证码")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "验证码发送成功",
            content = @Content(mediaType = "application/json",
                examples = @ExampleObject(value = "{\"message\":\"验证码发送成功: 123456\"}"))),
        @ApiResponse(responseCode = "500", description = "验证码发送失败",
            content = @Content(mediaType = "application/json",
                examples = @ExampleObject(value = "{\"message\":\"验证码发送失败: 错误信息\"}")))
    })
    @GetMapping("/send-code")
    public String sendSmsCode(
        @Parameter(description = "手机号码", required = true, example = "13800138000")
        @RequestParam String phoneNumber) {
        try {
            String code = aliSmsService.generateAndSendSmsCode(phoneNumber);
            return "验证码发送成功: " + code;
        } catch (Exception e) {
            return "验证码发送失败: " + e.getMessage();
        }
    }
}