package com.ywcx.cx_ai_slightly.controller;

import com.ywcx.cx_ai_slightly.common.Result;
import com.ywcx.cx_ai_slightly.entity.resp.SystemInfoResponse;
import com.ywcx.cx_ai_slightly.entity.resp.UserMockResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
/**
 * Swagger测试控制器
 * 
 * 用于演示Swagger文档生成功能的测试接口
 */
@RestController
@RequestMapping("/api/swagger-test")
@Slf4j
@Tag(name = "Swagger测试", description = "用于演示Swagger文档生成功能的测试接口")
public class SwaggerTestController {

    @Operation(summary = "Hello World", description = "简单的问候接口")
    @ApiResponse(responseCode = "200", description = "成功返回问候信息")
    @GetMapping("/hello")
    public Result<String> hello() {
        return Result.success("Hello, Swagger!");
    }

    @Operation(summary = "获取系统信息", description = "返回当前系统的基本信息")
    @ApiResponse(responseCode = "200", description = "成功返回系统信息",
        content = @Content(mediaType = "application/json",
            examples = @ExampleObject(value = "{\"code\":200,\"message\":\"成功\",\"data\":{\"time\":\"2024-01-01T12:00:00\",\"version\":\"1.0.0\"}}")))
    @GetMapping("/info")
    public Result<SystemInfoResponse> getSystemInfo() {
        SystemInfoResponse info = SystemInfoResponse.builder()
                .time(LocalDateTime.now())
                .version("1.0.0")
                .description("CX AI Slightly 系统")
                .build();
        return Result.success(info);
    }

    @Operation(summary = "Echo测试", description = "回显用户输入的消息")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "成功回显消息"),
        @ApiResponse(responseCode = "400", description = "参数错误")
    })
    @PostMapping("/echo")
    public Result<String> echo(
        @Parameter(description = "要回显的消息", required = true, example = "Hello World")
        @RequestParam String message) {
        
        if (message == null || message.trim().isEmpty()) {
            return Result.error(400, "消息不能为空");
        }
        
        return Result.success("Echo: " + message);
    }

    @Operation(summary = "用户信息", description = "根据用户ID获取用户信息")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "成功获取用户信息"),
        @ApiResponse(responseCode = "404", description = "用户不存在")
    })
    @GetMapping("/user/{userId}")
    public Result<UserMockResponse> getUserInfo(
        @Parameter(description = "用户ID", required = true, example = "123")
        @PathVariable Long userId) {
        
        UserMockResponse user = UserMockResponse.builder()
                .id(userId)
                .name("测试用户" + userId)
                .email("user" + userId + "@example.com")
                .createTime(LocalDateTime.now())
                .build();
        
        return Result.success(user);
    }
}