package com.ywcx.cx_ai_slightly;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * 应用主启动类
 * 
 * 该应用是一个基于Spring Boot的AI聊天服务，集成了以下主要功能：
 * - LangChain4j AI对话服务
 * - Sa-Token权限认证
 * - Redis缓存
 * - Spring Data JPA数据库操作
 */
@SpringBootApplication
public class CxAiSlightlyApplication {

    /**
     * 应用程序入口点
     * 
     * @param args 命令行参数
     */
    public static void main(String[] args) {
        SpringApplication.run(CxAiSlightlyApplication.class, args);
    }
}
