package com.ywcx.cx_ai_slightly.exception;

import cn.dev33.satoken.exception.NotLoginException;
import cn.dev33.satoken.exception.NotPermissionException;
import cn.dev33.satoken.exception.NotRoleException;
import cn.dev33.satoken.util.SaResult;
import com.ywcx.cx_ai_slightly.common.Result;
import com.ywcx.cx_ai_slightly.enums.ErrorCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;

import jakarta.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 全局异常处理器
 * 
 * 统一处理系统中的各种异常，包括：
 * - Sa-Token认证授权异常
 * - 业务逻辑异常
 * - 参数验证异常
 * - 系统运行时异常
 * 
 * 确保所有异常都能被妥善处理并返回统一格式的错误响应
 */
@RestControllerAdvice
@Slf4j
public class GlobalExceptionHandler {

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 处理Sa-Token未登录异常
     * 
     * 当用户未登录或Token无效时触发此异常
     * 根据不同的异常类型返回相应的错误信息
     * 
     * @param e NotLoginException Sa-Token未登录异常
     * @return SaResult 包含具体错误信息的响应
     */
    @ExceptionHandler(NotLoginException.class)
    @ResponseStatus(HttpStatus.UNAUTHORIZED)
    public SaResult handleNotLoginException(NotLoginException e) {
        String message;
        String errorType;
        
        // 根据异常类型确定具体的错误信息
        switch (e.getType()) {
            case NotLoginException.NOT_TOKEN:
                message = "未提供认证Token，请先登录";
                errorType = "NOT_TOKEN";
                break;
            case NotLoginException.INVALID_TOKEN:
                message = "Token格式无效，请重新登录";
                errorType = "INVALID_TOKEN";
                break;
            case NotLoginException.TOKEN_TIMEOUT:
                message = "Token已过期，请重新登录";
                errorType = "TOKEN_TIMEOUT";
                break;
            case NotLoginException.BE_REPLACED:
                message = "账号在其他设备登录，当前Token已失效";
                errorType = "BE_REPLACED";
                break;
            case NotLoginException.KICK_OUT:
                message = "账号已被管理员踢下线";
                errorType = "KICK_OUT";
                break;
            default:
                message = "当前会话未登录，请先登录";
                errorType = "NOT_LOGIN";
                break;
        }
        
        log.warn("用户未登录访问受保护资源 - 异常类型: {}, 错误信息: {}", errorType, e.getMessage());
        
        return SaResult.error(message)
                .setCode(401)
                .set("errorType", errorType)
                .set("timestamp", LocalDateTime.now().format(DATE_TIME_FORMATTER));
    }

    /**
     * 处理Sa-Token权限不足异常
     * 
     * 当用户没有访问特定资源的权限时触发
     * 
     * @param e NotPermissionException 权限不足异常
     * @return SaResult 权限不足的错误响应
     */
    @ExceptionHandler(NotPermissionException.class)
    @ResponseStatus(HttpStatus.FORBIDDEN)
    public SaResult handleNotPermissionException(NotPermissionException e) {
        String requiredPermission = e.getPermission();
        String message = String.format("权限不足，需要权限: %s", requiredPermission);
        
        log.warn("用户权限不足 - 需要权限: {}, 异常信息: {}", requiredPermission, e.getMessage());
        
        return SaResult.error(message)
                .setCode(403)
                .set("requiredPermission", requiredPermission)
                .set("timestamp", LocalDateTime.now().format(DATE_TIME_FORMATTER));
    }

    /**
     * 处理Sa-Token角色不足异常
     * 
     * 当用户没有访问特定资源所需的角色时触发
     * 
     * @param e NotRoleException 角色不足异常
     * @return SaResult 角色不足的错误响应
     */
    @ExceptionHandler(NotRoleException.class)
    @ResponseStatus(HttpStatus.FORBIDDEN)
    public SaResult handleNotRoleException(NotRoleException e) {
        String requiredRole = e.getRole();
        String message = String.format("角色权限不足，需要角色: %s", requiredRole);
        
        log.warn("用户角色不足 - 需要角色: {}, 异常信息: {}", requiredRole, e.getMessage());
        
        return SaResult.error(message)
                .setCode(403)
                .set("requiredRole", requiredRole)
                .set("timestamp", LocalDateTime.now().format(DATE_TIME_FORMATTER));
    }

    /**
     * 处理业务逻辑异常
     * 
     * 处理自定义的业务异常，返回具体的业务错误信息
     * 
     * @param e BusinessException 业务异常
     * @return Result 业务异常响应
     */
    @ExceptionHandler(BusinessException.class)
    public Result<Object> handleBusinessException(BusinessException e) {
        ErrorCode errorCode = e.getErrorCode();
        String customMessage = e.getCustomMessage();
        
        // 使用自定义消息或默认错误码消息
        String message = customMessage != null ? customMessage : errorCode.getMessage();
        
        log.warn("业务异常 - 错误码: {}, 错误信息: {}", errorCode.getCode(), message);
        
        return Result.error(errorCode.getCode(), message);
    }

    /**
     * 处理参数验证异常
     * 
     * 处理@Valid注解验证失败的异常
     * 
     * @param e MethodArgumentNotValidException 参数验证异常
     * @return Result 参数验证错误响应
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result<Map<String, String>> handleValidationException(MethodArgumentNotValidException e) {
        Map<String, String> errors = e.getBindingResult()
                .getFieldErrors()
                .stream()
                .collect(Collectors.toMap(
                        FieldError::getField,
                        FieldError::getDefaultMessage,
                        (existing, replacement) -> existing
                ));
        
        log.warn("参数验证失败 - 错误字段: {}", errors);
        
        return Result.<Map<String, String>>error(ErrorCode.PARAM_ERROR.getCode(), "参数验证失败")
                .data(errors);
    }

    /**
     * 处理绑定异常
     * 
     * 处理表单数据绑定失败的异常
     * 
     * @param e BindException 绑定异常
     * @return Result 绑定错误响应
     */
    @ExceptionHandler(BindException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result<Map<String, String>> handleBindException(BindException e) {
        Map<String, String> errors = e.getBindingResult()
                .getFieldErrors()
                .stream()
                .collect(Collectors.toMap(
                        FieldError::getField,
                        FieldError::getDefaultMessage,
                        (existing, replacement) -> existing
                ));
        
        log.warn("数据绑定失败 - 错误字段: {}", errors);
        
        return Result.<Map<String, String>>error(ErrorCode.PARAM_ERROR.getCode(), "数据绑定失败")
                .data(errors);
    }

    /**
     * 处理参数类型不匹配异常
     * 
     * 当请求参数类型与方法参数类型不匹配时触发
     * 
     * @param e MethodArgumentTypeMismatchException 参数类型不匹配异常
     * @return Result 参数类型错误响应
     */
    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result<Map<String, Object>> handleTypeMismatchException(MethodArgumentTypeMismatchException e) {
        String paramName = e.getName();
        String requiredType = e.getRequiredType() != null ? e.getRequiredType().getSimpleName() : "未知";
        Object rejectedValue = e.getValue();
        
        Map<String, Object> errorDetails = new HashMap<>();
        errorDetails.put("paramName", paramName);
        errorDetails.put("rejectedValue", rejectedValue);
        errorDetails.put("requiredType", requiredType);
        
        String message = String.format("参数 '%s' 类型错误，期望类型: %s，实际值: %s", 
                paramName, requiredType, rejectedValue);
        
        log.warn("参数类型不匹配 - {}", message);
        
        return Result.<Map<String, Object>>error(ErrorCode.PARAM_ERROR.getCode(), message)
                .data(errorDetails);
    }

    /**
     * 处理所有未捕获的异常
     * 
     * 作为最后的异常处理器，捕获所有其他未处理的异常
     * 避免异常信息直接暴露给用户，同时记录详细的错误日志
     * 
     * @param e Exception 未捕获的异常
     * @param request HttpServletRequest 请求对象
     * @return SaResult 系统异常响应
     */
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public SaResult handleException(Exception e, HttpServletRequest request) {
        String requestUri = request != null ? request.getRequestURI() : "未知";
        String method = request != null ? request.getMethod() : "未知";
        
        // 记录详细的异常信息用于调试
        log.error("系统异常 - 请求路径: {} {}, 异常类型: {}, 异常信息: {}", 
                method, requestUri, e.getClass().getSimpleName(), e.getMessage(), e);
        
        // 生成错误追踪ID，便于问题排查
        String errorId = "ERR_" + System.currentTimeMillis();
        
        return SaResult.error("系统内部错误，请联系管理员")
                .setCode(500)
                .set("errorId", errorId)
                .set("timestamp", LocalDateTime.now().format(DATE_TIME_FORMATTER));
    }
}
