package com.ywcx.cx_ai_slightly.exception;

import com.ywcx.cx_ai_slightly.enums.ErrorCode;

/**
 * 业务异常类
 * 
 * 用于处理业务逻辑中的异常情况，提供统一的异常处理机制
 * 支持使用预定义的错误码或自定义错误信息
 */
public class BusinessException extends RuntimeException {

    /**
     * 错误码
     */
    private final ErrorCode errorCode;

    /**
     * 自定义错误信息
     */
    private final String customMessage;

    /**
     * 构造函数 - 使用错误码
     * 
     * @param errorCode 错误码枚举
     */
    public BusinessException(ErrorCode errorCode) {
        super(errorCode.getMessage());
        this.errorCode = errorCode;
        this.customMessage = null;
    }

    /**
     * 构造函数 - 使用错误码和自定义消息
     * 
     * @param errorCode 错误码枚举
     * @param customMessage 自定义错误信息
     */
    public BusinessException(ErrorCode errorCode, String customMessage) {
        super(customMessage != null ? customMessage : errorCode.getMessage());
        this.errorCode = errorCode;
        this.customMessage = customMessage;
    }

    /**
     * 构造函数 - 使用错误码、自定义消息和原因
     * 
     * @param errorCode 错误码枚举
     * @param customMessage 自定义错误信息
     * @param cause 异常原因
     */
    public BusinessException(ErrorCode errorCode, String customMessage, Throwable cause) {
        super(customMessage != null ? customMessage : errorCode.getMessage(), cause);
        this.errorCode = errorCode;
        this.customMessage = customMessage;
    }

    /**
     * 构造函数 - 使用错误码和原因
     * 
     * @param errorCode 错误码枚举
     * @param cause 异常原因
     */
    public BusinessException(ErrorCode errorCode, Throwable cause) {
        super(errorCode.getMessage(), cause);
        this.errorCode = errorCode;
        this.customMessage = null;
    }

    /**
     * 获取错误码
     * 
     * @return ErrorCode 错误码枚举
     */
    public ErrorCode getErrorCode() {
        return errorCode;
    }

    /**
     * 获取自定义错误信息
     * 
     * @return String 自定义错误信息，可能为null
     */
    public String getCustomMessage() {
        return customMessage;
    }

    /**
     * 获取错误码的数字值
     * 
     * @return int 错误码数字
     */
    public int getCode() {
        return errorCode.getCode();
    }

    /**
     * 获取最终的错误信息
     * 优先使用自定义消息，如果没有则使用错误码默认消息
     * 
     * @return String 最终的错误信息
     */
    public String getFinalMessage() {
        return customMessage != null ? customMessage : errorCode.getMessage();
    }

    @Override
    public String toString() {
        return String.format("BusinessException{errorCode=%s, code=%d, message='%s', customMessage='%s'}", 
                errorCode.name(), errorCode.getCode(), errorCode.getMessage(), customMessage);
    }

    // =============================静态工厂方法=============================

    /**
     * 创建系统错误异常
     * 
     * @return BusinessException 系统错误异常
     */
    public static BusinessException systemError() {
        return new BusinessException(ErrorCode.SYSTEM_ERROR);
    }

    /**
     * 创建系统错误异常
     * 
     * @param message 自定义错误信息
     * @return BusinessException 系统错误异常
     */
    public static BusinessException systemError(String message) {
        return new BusinessException(ErrorCode.SYSTEM_ERROR, message);
    }

    /**
     * 创建参数错误异常
     * 
     * @return BusinessException 参数错误异常
     */
    public static BusinessException paramError() {
        return new BusinessException(ErrorCode.PARAM_ERROR);
    }

    /**
     * 创建参数错误异常
     * 
     * @param message 自定义错误信息
     * @return BusinessException 参数错误异常
     */
    public static BusinessException paramError(String message) {
        return new BusinessException(ErrorCode.PARAM_ERROR, message);
    }

    /**
     * 创建网络错误异常
     * 
     * @return BusinessException 网络错误异常
     */
    public static BusinessException networkError() {
        return new BusinessException(ErrorCode.NETWORK_ERROR);
    }

    /**
     * 创建网络错误异常
     * 
     * @param message 自定义错误信息
     * @return BusinessException 网络错误异常
     */
    public static BusinessException networkError(String message) {
        return new BusinessException(ErrorCode.NETWORK_ERROR, message);
    }

    /**
     * 创建AI服务错误异常
     * 
     * @return BusinessException AI服务错误异常
     */
    public static BusinessException aiServiceError() {
        return new BusinessException(ErrorCode.AI_SERVICE_ERROR);
    }

    /**
     * 创建AI服务错误异常
     * 
     * @param message 自定义错误信息
     * @return BusinessException AI服务错误异常
     */
    public static BusinessException aiServiceError(String message) {
        return new BusinessException(ErrorCode.AI_SERVICE_ERROR, message);
    }

    /**
     * 创建未授权访问异常
     * 
     * @return BusinessException 未授权访问异常
     */
    public static BusinessException unauthorized() {
        return new BusinessException(ErrorCode.UNAUTHORIZED);
    }

    /**
     * 创建未授权访问异常
     * 
     * @param message 自定义错误信息
     * @return BusinessException 未授权访问异常
     */
    public static BusinessException unauthorized(String message) {
        return new BusinessException(ErrorCode.UNAUTHORIZED, message);
    }

    /**
     * 创建禁止访问异常
     * 
     * @return BusinessException 禁止访问异常
     */
    public static BusinessException forbidden() {
        return new BusinessException(ErrorCode.FORBIDDEN);
    }

    /**
     * 创建禁止访问异常
     * 
     * @param message 自定义错误信息
     * @return BusinessException 禁止访问异常
     */
    public static BusinessException forbidden(String message) {
        return new BusinessException(ErrorCode.FORBIDDEN, message);
    }

    /**
     * 创建资源未找到异常
     * 
     * @return BusinessException 资源未找到异常
     */
    public static BusinessException notFound() {
        return new BusinessException(ErrorCode.RESOURCE_NOT_FOUND);
    }

    /**
     * 创建资源未找到异常
     * 
     * @param message 自定义错误信息
     * @return BusinessException 资源未找到异常
     */
    public static BusinessException notFound(String message) {
        return new BusinessException(ErrorCode.RESOURCE_NOT_FOUND, message);
    }
}